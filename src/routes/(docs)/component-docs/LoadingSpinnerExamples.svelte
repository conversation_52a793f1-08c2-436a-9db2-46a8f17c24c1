<script lang="ts">
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Button from '$lib/components/Button.svelte';

  let showLoadingDemo = false;
  let loadingProgress = 0;

  function simulateLoading() {
    showLoadingDemo = true;
    loadingProgress = 0;
    
    const interval = setInterval(() => {
      loadingProgress += 10;
      if (loadingProgress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          showLoadingDemo = false;
          loadingProgress = 0;
        }, 1000);
      }
    }, 200);
  }
</script>

<div class="component-docs-container">
  <h1>Loading Spinner Components</h1>
  <p class="description">Examples and usage of the LoadingSpinner component with different sizes and messages.</p>

  <section>
    <h2>Different Sizes</h2>
    <p>Loading spinners come in three sizes: small, medium, and large.</p>
    
    <div class="spinner-row">
      <div class="spinner-demo">
        <h4>Small</h4>
        <LoadingSpinner size="small" message="Loading..." />
      </div>
      
      <div class="spinner-demo">
        <h4>Medium (Default)</h4>
        <LoadingSpinner size="medium" message="Loading..." />
      </div>
      
      <div class="spinner-demo">
        <h4>Large</h4>
        <LoadingSpinner size="large" message="Loading..." />
      </div>
    </div>
  </section>

  <section>
    <h2>Custom Messages</h2>
    <p>Customize the loading message to provide context to users.</p>
    
    <div class="spinner-row">
      <div class="spinner-demo">
        <LoadingSpinner message="Saving your changes..." />
      </div>
      
      <div class="spinner-demo">
        <LoadingSpinner message="Uploading files..." />
      </div>
      
      <div class="spinner-demo">
        <LoadingSpinner message="Processing data..." />
      </div>
    </div>
  </section>

  <section>
    <h2>No Message</h2>
    <p>Loading spinner without any text message.</p>
    
    <div class="spinner-row">
      <div class="spinner-demo">
        <LoadingSpinner message="" />
      </div>
    </div>
  </section>

  <section>
    <h2>Inline and White Variants</h2>
    <p>Compact inline spinners perfect for buttons and inline contexts.</p>
    
    <div class="variant-examples">
      <div class="variant-demo">
        <h4>Inline Variant</h4>
        <p>Perfect for inline text and secondary buttons:</p>
        <div class="inline-examples">
          <p>Processing <LoadingSpinner variant="inline" message="" /> please wait...</p>
          <Button variant="secondary" disabled>
            <LoadingSpinner variant="inline" message="" />
            Loading...
          </Button>
        </div>
      </div>
      
      <div class="variant-demo">
        <h4>White Variant</h4>
        <p>Designed for primary buttons with dark backgrounds:</p>
        <div class="white-examples">
          <Button variant="primary" disabled>
            <LoadingSpinner variant="white" message="" />
            Saving...
          </Button>
          <Button variant="primary" disabled>
            <LoadingSpinner variant="white" message="" />
            Uploading...
          </Button>
        </div>
      </div>
    </div>
  </section>

  <section>
    <h2>Interactive Demo</h2>
    <p>Click the button to see a simulated loading process.</p>
    
    <div class="interactive-demo">
      {#if !showLoadingDemo}
        <Button on:click={simulateLoading}>Start Loading Demo</Button>
      {:else}
        <div class="loading-demo-container">
          <LoadingSpinner message="Processing your request..." />
          <div class="progress-info">
            <div class="progress-bar">
              <div class="progress-fill" style="width: {loadingProgress}%"></div>
            </div>
            <span class="progress-text">{loadingProgress}% Complete</span>
          </div>
        </div>
      {/if}
    </div>
  </section>

  <section>
    <h2>In Context Examples</h2>
    <p>Examples of how loading spinners might appear in real application contexts.</p>
    
    <div class="context-examples">
      <div class="context-card">
        <h4>Card Loading State</h4>
        <div class="card-content">
          <LoadingSpinner size="small" message="Loading content..." />
        </div>
      </div>
      
      <div class="context-card">
        <h4>Form Submission</h4>
        <div class="form-example">
          <input type="text" placeholder="Enter your name" disabled />
          <input type="email" placeholder="Enter your email" disabled />
          <div class="form-loading">
            <LoadingSpinner size="small" message="Submitting form..." />
          </div>
        </div>
      </div>
      
      <div class="context-card">
        <h4>Data Table Loading</h4>
        <div class="table-loading">
          <LoadingSpinner message="Loading table data..." />
        </div>
      </div>
    </div>
  </section>
</div>

<style lang="less">
  

    
    section {
      background: var(--bg);
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--border);
    

      p {
        margin-bottom: 1.5rem;
        color: var(--grey);
        line-height: 1.5;
      }
    }

    .spinner-row {
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
      justify-content: center;
    }

    .spinner-demo {
      text-align: center;
      padding: 1.5rem;
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      min-width: 200px;
    }

    .interactive-demo {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
    }

    .loading-demo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1.5rem;
    }

    .progress-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      width: 100%;
      max-width: 300px;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: var(--bg);
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: var(--primary);
      transition: width 0.2s ease;
    }

    .progress-text {
      font-size: 0.9rem;
      color: var(--grey);
    }

    .context-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .context-card {
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      padding: 1.5rem;

      h4 {
        margin-bottom: 1rem;
        color: var(--primary);
      }
    }

    .card-content {
      min-height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg);
      border-radius: 6px;
    }

    .form-example {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      input {
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;
        background: var(--bg);
        color: var(--grey);
      }

      .form-loading {
        display: flex;
        justify-content: center;
        padding: 1rem;
      }
    }

    .table-loading {
      min-height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg);
      border-radius: 6px;
      border: 1px dashed var(--border);
    }

    .variant-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }

    .variant-demo {
      background: white;
      border-radius: 8px;
      border: 1px solid var(--border);
      padding: 1.5rem;

      h4 {
        margin-bottom: 1rem;
        color: var(--primary);
      }

      p {
        margin-bottom: 1rem;
        color: var(--grey);
      }
    }

    .inline-examples {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;

      p {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        padding: 0.75rem;
        background: var(--bg);
        border-radius: 4px;
        border: 1px solid var(--border);
      }
    }

    .white-examples {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }
  
</style> 