import { json } from '@sveltejs/kit';
import puppeteer from 'puppeteer';
import { readFileSync } from 'fs';
import { join } from 'path';
import type { RequestHandler } from './$types';

// Server-safe currency formatting
function formatCurrencyServer(amount: number): string {
  return new Intl.NumberFormat('en-GB', {
    style: 'currency',
    currency: 'GBP'
  }).format(amount);
}

// Server-safe status display
function getStatusDisplayServer(status: number): { name: string; color: string } {
  const statusMap: Record<number, { name: string; color: string }> = {
    0: { name: 'Draft', color: '#6b7280' },
    1: { name: 'Sent', color: '#3b82f6' },
    2: { name: 'Paid', color: '#10b981' },
    3: { name: 'Overdue', color: '#ef4444' },
    4: { name: 'Cancelled', color: '#6b7280' }
  };
  
  return statusMap[status] || { name: 'Unknown', color: '#6b7280' };
}

// Function to get the logo as base64
function getLogoBase64(): string {
  try {
    const logoPath = join(process.cwd(), 'static', 'cbc-logo.svg');
    const logoBuffer = readFileSync(logoPath);
    const logoBase64 = logoBuffer.toString('base64');
    return `data:image/svg+xml;base64,${logoBase64}`;
  } catch (error) {
    console.error('Error reading logo file:', error);
    return '';
  }
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { invoice } = await request.json();
    
    if (!invoice) {
      return json({ error: 'Invoice data is required' }, { status: 400 });
    }

    // Generate HTML for the invoice
    const html = generateInvoiceHTML(invoice);
    
    // Launch Puppeteer
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    const page = await browser.newPage();
    
    // Set the content with proper HTML structure
    await page.setContent(html, {
      waitUntil: 'networkidle0'
    });
    
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0mm',
        right: '0mm',
        bottom: '0mm',
        left: '0mm'
      }
    });
    
    await browser.close();
    
    // Return the PDF as a response
    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${invoice.invoiceNumber || 'draft'}.pdf"`
      }
    });
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    return json({ 
      error: 'Failed to generate PDF', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
};

function generateInvoiceHTML(invoice: any): string {
  const invoiceNumber = invoice.invoiceNumber || 'Draft';
  const issueDate = invoice.issueDate ? new Date(invoice.issueDate).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric'
  }) : 'Not set';
  const dueDate = invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric'
  }) : 'Not set';
  const statusDisplay = getStatusDisplayServer(invoice.status || 0);
  
  // Calculate totals
  const subtotal = invoice.invoiceLines?.reduce((sum: number, line: any) => sum + (line.total || 0), 0) || 0;
  const taxAmount = invoice.invoiceLines?.reduce((sum: number, line: any) => sum + (line.taxAmount || 0), 0) || 0;
  const total = subtotal + taxAmount;

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice ${invoiceNumber}</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-hs: 236 46%;
      --primary-l: 20%;
      --primary: hsl(var(--primary-hs) var(--primary-l));
      --grey: #4f5561;
      --black: #222;
      --white: #fff;
      --bg: hsl(220, 60%, 98.04%);
      --border: hsl(240, 55%, 89%);
      --br: 10px;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      font-size: 14px;
      line-height: 1.6;
      color: var(--black);
      background: white;
    }
    
    .a4-canvas {
      background: white;
      padding: 4rem;
      display: grid;
      gap: 1rem;
      max-width: 210mm;
      margin: 0 auto;
      box-sizing: border-box;
    }
    
    .layout-row {
      display: grid;
      gap: 2rem;
      align-items: start;
    }
    
    .layout-column {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    /* Row 1: Invoice Header */
    .row-1 { grid-template-columns: 1fr 1fr; }
    
    /* Row 2: Customer and Company Details */
    .row-2 { grid-template-columns: 1fr 1fr; }
    
    /* Row 3: Invoice Details */
    .row-3 { grid-template-columns: 1fr 1fr; }
    
    /* Row 4: Line Items */
    .row-4 { grid-template-columns: 1fr; }
    
    /* Row 5: Notes and Terms */
    .row-5 { grid-template-columns: 1fr 1fr; }
    
    /* Row 6: Footer */
    .row-6 { grid-template-columns: 1fr; }
    
    /* DocumentHeader Component */
    .invoice-title-section h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: bold;
      color: var(--primary);
    }
    
    .invoice-title-section .invoice-number {
      font-size: 1.2rem;
      color: var(--grey);
      margin-top: 0.5rem;
    }
    
    /* CompanyLogo Component */
    .brand-logo {
      width: 100px;
      height: auto;
      margin-bottom: 20px;
      align-self: flex-end;
    }
    
    /* CustomerDetails Component */
    .customer-details {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .customer-details h3 {
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .customer-details p {
      font-size: 0.8rem;
      line-height: 1.5;
      margin: 0;
      color: var(--black);
    }
    
    /* CompanyDetails Component */
    .company-details {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .company-details h3 {
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .company-details p {
      font-size: 0.8rem;
      line-height: 1.5;
      margin: 0;
    }
    
    /* HeaderFields Component */
    .invoice-details {
      display: flex;
      flex-direction: column;
    }
    
    .invoice-details h3 {
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    
    .form-group {
      margin-bottom: 0px;
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    
    .form-group label {
      width: 140px;
      margin-bottom: 0px;
      display: block;
      font-weight: 500;
      font-size: 0.8rem;
      color: var(--grey);
    }
    
    .readonly-text {
      margin: 0;
      color: var(--black);
      font-size: 14px;
      line-height: 1.5;
    }
    
    /* Line Items Table */
    .line-items-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
      margin: 1rem 0;
    }
    
    .line-items-table th {
      background-color: #f3f4f6;
      border: 1px solid #e5e7eb;
      padding: 12px;
      text-align: left;
      font-weight: 600;
      color: var(--black);
    }
    
    .line-items-table th:nth-child(2) { text-align: center; }
    .line-items-table th:nth-child(3),
    .line-items-table th:nth-child(4),
    .line-items-table th:nth-child(5) { text-align: right; }
    
    .line-items-table td {
      border: 1px solid #e5e7eb;
      padding: 12px;
      vertical-align: top;
    }
    
    .line-items-table td:nth-child(2) { text-align: center; }
    .line-items-table td:nth-child(3),
    .line-items-table td:nth-child(4),
    .line-items-table td:nth-child(5) { text-align: right; }
    
    /* Bank Details and Totals Row */
    .bank-totals-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      margin-top: 1rem;
      gap: 2rem;
    }
    
    .bank-details-container {
      padding-top: 0.5rem;
    }
    
    .bank-details h3 {
      margin-bottom: 0.5rem;
      font-weight: 600;
    }
    
    .bank-detail-row {
      display: flex;
      align-items: center;
      margin-bottom: 0.3rem;
    }
    
    .bank-detail-row label {
      width: 140px;
      margin-bottom: 0;
      flex-shrink: 0;
      display: block;
      font-weight: 500;
      font-size: 0.8rem;
      color: var(--grey);
    }
    
    .bank-detail-row .bank-value {
      color: var(--black);
      font-size: 14px;
      line-height: 1.4;
    }
    
    /* Totals Section */
    .totals-section {
      display: flex;
      justify-content: flex-end;
    }
    
    .totals-box {
      border-radius: 4px;
      width: 100%;
      box-sizing: border-box;
      margin-left: auto;
      padding-right: 1rem;
    }
    
    .totals-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--border);
    }
    
    .totals-row:last-child {
      border-bottom: none;
      border-top: 1px solid var(--border);
      padding-top: 12px;
      font-weight: 600;
      font-size: 1.1em;
      color: var(--black);
    }
    
    .totals-row:last-child .totals-value {
      font-size: 1.3em;
      font-weight: 600;
    }
    
    .totals-row:last-child .totals-label {
      font-size: 1.3em;
    }
    
    .totals-label {
      color: var(--grey);
      font-weight: 500;
    }
    
    .totals-value {
      font-weight: 500;
      color: var(--black);
      text-align: right;
    }
    
    /* Notes and Terms */
    .notes-terms {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .notes-terms h3 {
      margin-bottom: 0rem;
      font-weight: 600;
    }
    
    .notes-terms .readonly-text {
      margin: 0;
      border-radius: 4px;
      color: var(--black);
      font-size: 14px;
      line-height: 1.5;
      white-space: pre-wrap;
    }
    
    .notes-terms .readonly-text.no-content {
      color: var(--grey);
      font-style: italic;
    }
    
    /* Footer */
    .footer-fields {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      color: var(--grey);
      font-size: 0.8rem;
    }
    
    .footer-fields p {
      margin: 0;
      line-height: 1.5;
    }
    
    @media print {
      body { -webkit-print-color-adjust: exact; }
    }
  </style>
</head>
<body>
  <div class="a4-canvas">
    
    <!-- Row 1: Invoice Header -->
    <div class="layout-row row-1">
      <div class="layout-column">
        <div class="invoice-title-section">
          <h1>INVOICE</h1>
          <div class="invoice-number">#${invoiceNumber}</div>
        </div>
      </div>
      <div class="layout-column">
        <img src="${getLogoBase64()}" alt="CBC Logo" class="brand-logo" />
      </div>
    </div>

    <!-- Row 2: Company and Customer Info -->
    <div class="layout-row row-2">
      <div class="layout-column">
        <div class="customer-details">
          <h3>To</h3>
          <p>
            Customer Name<br>
            123 Customer Street<br>
            City, State 12345<br>
            Phone: (*************<br>
            Email: <EMAIL>
          </p>
        </div>
      </div>
      
      <div class="layout-column">
        <div class="company-details">
          <h3>From</h3>
          <p>
            Custom Built Campervans,<br/>
            Unit 3 and 4,<br/>
            Starnhill Close,<br/>
            Ecclesfield,<br/>
            Sheffield,<br/>
            S35 9TG
          </p>
        </div>
      </div>
    </div>

    <!-- Row 3: Invoice Details -->
    <div class="layout-row row-3">
      <div class="layout-column">
        <!-- Empty left column to match layout -->
      </div>
      
      <div class="layout-column">
        <div class="invoice-details">
          <h3>Invoice Details</h3>
          
          <div class="form-group">
            <label>Invoice No.</label>
            <p class="readonly-text">${invoiceNumber}</p>
          </div>
          
          <div class="form-group">
            <label>Issue Date</label>
            <p class="readonly-text">${issueDate}</p>
          </div>
          
          <div class="form-group">
            <label>Due Date</label>
            <p class="readonly-text">${dueDate}</p>
          </div>
          
          ${invoice.reference ? `
          <div class="form-group">
            <label>Reference</label>
            <p class="readonly-text">${invoice.reference}</p>
          </div>
          ` : ''}
        </div>
      </div>
    </div>

    <!-- Row 4: Line Items -->
    <div class="layout-row row-4">
      <div class="layout-column">
        <table class="line-items-table">
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Unit Price</th>
              <th>Tax Rate (%)</th>
              <th>Line Total</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.invoiceLines?.map((line: any) => `
              <tr>
                <td>${line.description || ''}</td>
                <td>${line.quantity || 0}</td>
                <td>${formatCurrencyServer(line.unitPrice || 0)}</td>
                <td>${((line.taxRate || 0) * 100).toFixed(1)}%</td>
                <td>${formatCurrencyServer(line.total || 0)}</td>
              </tr>
            `).join('') || ''}
          </tbody>
        </table>
        
        <!-- Bank Details and Totals Row -->
        <div class="bank-totals-row">
          <div class="bank-details-container">
            <div class="bank-details">
              <h3>Bank Details</h3>
              
              <div class="bank-detail-row">
                <label>Account Name</label>
                <div class="bank-value">Custom Built Campervans</div>
              </div>
              
              <div class="bank-detail-row">
                <label>Account Number</label>
                <div class="bank-value">**********</div>
              </div>
              
              <div class="bank-detail-row">
                <label>Sort Code</label>
                <div class="bank-value">123456</div>
              </div>
            </div>
          </div>
          
          <div class="totals-section">
            <div class="totals-box">
              <div class="totals-row">
                <span class="totals-label">Subtotal:</span>
                <span class="totals-value">${formatCurrencyServer(subtotal)}</span>
              </div>
              <div class="totals-row">
                <span class="totals-label">Tax:</span>
                <span class="totals-value">${formatCurrencyServer(taxAmount)}</span>
              </div>
              <div class="totals-row">
                <span class="totals-label">Total:</span>
                <span class="totals-value">${formatCurrencyServer(total)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 5: Notes and Terms -->
    <div class="layout-row row-5">
      <div class="layout-column">
        <div class="notes-terms">
          <h3>Notes</h3>
          ${invoice.notes ? `
            <p class="readonly-text">${invoice.notes}</p>
          ` : `
            <p class="readonly-text no-content">No notes provided</p>
          `}
        </div>
      </div>
      
      <div class="layout-column">
        <div class="notes-terms">
          <h3>Terms and Conditions</h3>
          ${invoice.paymentTerms ? `
            <p class="readonly-text">${invoice.paymentTerms}</p>
          ` : `
            <p class="readonly-text no-content">No payment terms specified</p>
          `}
        </div>
      </div>
    </div>

    <!-- Row 6: Footer -->
    <div class="layout-row row-6" style="margin-top: 2rem;">
      <div class="layout-column">
        <div class="footer-fields">
          <p>Custom Built Camperans, Unit 3 and 4, Starnhill Close, Ecclesfield, Sheffield, S35 9TG</p>
          <p>VAT Number: 378 8556 28</p>
        </div>
      </div>
    </div>

  </div>
</body>
</html>
  `;
} 