<script lang="ts">
  import { onMount } from 'svelte';
  import { goto, beforeNavigate } from '$app/navigation';
  import { page } from '$app/stores';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { contactStore } from '$lib/stores/customerStore';
  import LineItems from '$lib/components/page/LineItems.svelte';
  import {
    getInvoiceById,
    saveInvoice,
    getProducts,
    getInvoiceTemplates,
    getInvoiceStatuses,
    calculateApiInvoiceTotals,
    calculateLineItemTotals,
    createEmptyApiLineItem,
    type ApiInvoice,
    type ApiInvoiceLine,
    type Product,
    type InvoiceTemplate,
    type InvoiceStatus
  } from '$lib/api/invoices';
  import { formatCurrency } from '$lib/config/currency';
  import { generateInvoicePDFFromLayout } from '$lib/utils/pdfGenerator';
  import { sendInvoiceEmail, getEmailHistoryForInvoice, type EmailHistoryItem, type EmailAttachment } from '$lib/api/emailService';
  import Modal from '$lib/components/Modal.svelte';
  import CompanyLogo from '$lib/components/page/CompanyLogo.svelte';
  import DocumentHeader from '$lib/components/page/DocumentHeader.svelte';
  import CompanyDetails from '$lib/components/page/CompanyDetails.svelte';
  import CustomerDetails from '$lib/components/page/CustomerDetails.svelte';
  import HeaderFields from '$lib/components/page/HeaderFields.svelte';
  import BankDetails from '$lib/components/page/BankDetails.svelte';
  import InvoiceNotes from '$lib/components/page/InvoiceNotes.svelte';
  import InvoiceTerms from '$lib/components/page/InvoiceTerms.svelte';
  import FooterFields from '$lib/components/page/FooterFields.svelte';

  // Define interfaces for our form data - now using API format directly
  interface InvoiceFormData {
    id?: string;
    invoiceNumber?: number;
    status: number; // API format: 0 = Draft, 1 = Sent, etc.
    issueDate: string;
    dueDate: string;
    reference?: string;
    notes?: string;
    paymentTerms?: string;
    invoiceLines: ApiInvoiceLine[];
    // Additional fields for UI only
    customerId: string;
    templateId?: string;
    discountAmount: number;
  }

  // Get invoice ID and mode from URL
  $: invoiceId = $page.params.invoiceId;
  $: isNewInvoice = invoiceId === 'new';
  $: isEditMode = $page.url.searchParams.get('edit') === 'true';

  let mode: 'edit' | 'readonly';
  $: mode = isNewInvoice || isEditMode ? 'edit' : 'readonly';

  // State variables
  let isLoading = false;
  let isSaving = false;
  let products: Product[] = [];
  let invoiceTemplates: InvoiceTemplate[] = [];
  let invoiceStatuses: InvoiceStatus[] = [];
  let isLoadingData = true;
  let customerSearch = '';
  let formChanged = false;
  let initialFormState: string;
  let taxMode: 0 | 1 = 0; // Default to Tax Exclusive
  let isNavigatingAfterSave = false; // Flag to track programmatic navigation after save

  // Email functionality
  let isSendingEmail = false;
  let emailHistory: EmailHistoryItem[] = [];
  let showEmailModal = false;
  let emailAddress = '';
  let emailSubject = '';
  let emailMessage = '';

  // Form data
  let formData: InvoiceFormData = {
    status: 0, // Draft
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    reference: '',
    notes: '',
    paymentTerms: '',
    invoiceLines: [],
    customerId: '',
    templateId: '',
    discountAmount: 0
  };

  // Form validation
  let errors: Record<string, string> = {};
  let formSubmitted = false;

  function updateFormChanged() {
    if (!initialFormState) return;
    const currentFormState = JSON.stringify({ formData });
    const hasChanged = currentFormState !== initialFormState;
    formChanged = hasChanged;
  }

  function calculateTotals() {
    // Calculate line totals and update each line
    const updatedLines = formData.invoiceLines.map(line => calculateLineItemTotals(line));

    formData = {
      ...formData,
      invoiceLines: updatedLines
    };
  }

  function addLineItem() {
    const newLineNumber = formData.invoiceLines.length + 1;
    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, createEmptyApiLineItem(newLineNumber)]
    };
    updateFormChanged();
  }

  function removeLineItem(index: number) {
    formData = {
      ...formData,
      invoiceLines: formData.invoiceLines.filter((_, i) => i !== index)
    };

    calculateTotals();
    updateFormChanged();
  }

  function updateLineItem(index: number, lineItem: ApiInvoiceLine) {
    const updatedLines = [...formData.invoiceLines];
    updatedLines[index] = lineItem;
    formData = {
      ...formData,
      invoiceLines: updatedLines
    };
    calculateTotals();
    updateFormChanged();
  }

  function validateForm(): boolean {
    errors = {};

    if (!formData.customerId) {
      errors.customerId = 'Please select a customer';
    }

    if (!formData.issueDate) {
      errors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      errors.dueDate = 'Due date is required';
    }

    // Validate each line item
    formData.invoiceLines.forEach((item, index) => {
      if (!item.description) {
        errors[`invoiceLines[${index}].description`] = 'Description is required';
      }

      if (item.quantity <= 0) {
        errors[`invoiceLines[${index}].quantity`] = 'Quantity must be greater than 0';
      }

      if (item.unitPrice < 0) {
        errors[`invoiceLines[${index}].unitPrice`] = 'Unit price cannot be negative';
      }
    });

    return Object.keys(errors).length === 0;
  }

  async function loadData() {
    isLoadingData = true;
    try {
      // Load contacts/customers
      await contactStore.loadContacts();

      // Load other data in parallel
      const [productsData, templatesData, statusesData] = await Promise.all([
        getProducts(),
        getInvoiceTemplates(),
        getInvoiceStatuses()
      ]);

      products = productsData;
      invoiceTemplates = templatesData;
      invoiceStatuses = statusesData;

      // Set default template
      if (invoiceTemplates.length > 0) {
        const defaultTemplate = invoiceTemplates.find(t => t.isDefault);
        if (defaultTemplate) {
          formData.templateId = defaultTemplate.id;
        }
      }

    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ message: 'Failed to load data', type: 'error' });
    } finally {
      isLoadingData = false;
    }
  }

  // Load existing invoice data if editing or viewing
  async function loadInvoiceData() {
    if (isNewInvoice) {
      // For new invoices, add one empty line item to start with
      addLineItem();
      return;
    }

    // isLoading is already set to true in onMount for existing invoices
    try {
      const invoice = await getInvoiceById(invoiceId);
      if (invoice) {
        formData = {
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          status: invoice.status,
          issueDate: invoice.issueDate,
          dueDate: invoice.dueDate,
          reference: invoice.reference || '',
          notes: invoice.notes || '',
          paymentTerms: invoice.paymentTerms || '',
          invoiceLines: invoice.invoiceLines || [],
          customerId: '', // Will need to be populated from customer data
          templateId: '',
          discountAmount: 0
        };

        // Load email history if viewing
        if (mode === 'readonly') {
          // Temporarily disabled to fix infinite loop
          // emailHistory = await getEmailHistoryForInvoice(invoiceId);

          // Set default email subject
          const invoiceTotal = formData.invoiceLines.reduce((sum: number, line: ApiInvoiceLine) => sum + line.total, 0);
          emailSubject = `Invoice ${formData.invoiceNumber || 'Draft'} - ${formatCurrency(invoiceTotal)}`;
        }
      }
    } catch (error) {
      console.error('Error loading invoice:', error);
      addToast({ message: 'Failed to load invoice', type: 'error' });
    } finally {
      isLoading = false;
    }
  }

  async function handleSubmit() {
    formSubmitted = true;
    calculateTotals();

    if (!validateForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    isSaving = true;

    try {
      // Prepare the API invoice data
      const apiInvoice: ApiInvoice = {
        id: formData.id,
        invoiceNumber: formData.invoiceNumber,
        status: formData.status,
        issueDate: formData.issueDate,
        dueDate: formData.dueDate,
        reference: formData.reference,
        notes: formData.notes,
        paymentTerms: formData.paymentTerms,
        invoiceLines: formData.invoiceLines
      };

      const savedInvoice = await saveInvoice(apiInvoice);

      addToast({
        message: isNewInvoice ? 'Invoice created successfully' : 'Invoice updated successfully',
        type: 'success'
      });

      // Reset form changed state and set navigation flag
      formChanged = false;
      isNavigatingAfterSave = true;

      // Navigate to view mode or back to invoices list
      if (isNewInvoice) {
        goto(`/invoices/${savedInvoice.id}`);
      } else {
        goto(`/invoices/${invoiceId}`);
      }
    } catch (err) {
      console.error('Error saving invoice:', err);
      addToast({
        message: err instanceof Error ? err.message : 'An unknown error occurred',
        type: 'error'
      });
    } finally {
      isSaving = false;
    }
  }

  function cancelForm() {
    updateFormChanged();
    if (formChanged) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        goto('/invoices');
      }
    } else {
      goto('/invoices');
    }
  }

  // Get calculated totals for display
  $: totals = calculateApiInvoiceTotals(formData.invoiceLines);

  // Handle navigation confirmation
  beforeNavigate(({ cancel, to }) => {
    // Skip if we're navigating after a successful save
    if (isNavigatingAfterSave) {
      isNavigatingAfterSave = false; // Reset the flag
      return;
    }
    
    // Skip if the form hasn't changed
    updateFormChanged();
    if (!formChanged) {
      return;
    }

    // Only show dialog if we're actually navigating to a different page
    if (to?.url.pathname !== window.location.pathname) {
      if (!window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        cancel();
      }
    }
  });

  onMount(async () => {
    // Set loading state immediately for existing invoices to prevent flash of empty content
    if (!isNewInvoice) {
      isLoading = true;
    }
    
    await loadData();
    await loadInvoiceData();
    // Set initial form state after data is loaded
    setTimeout(() => {
      const initialState = JSON.stringify({ formData });
      initialFormState = initialState;
    }, 100);
  });

  function handleTaxModeChange(event: CustomEvent<{ taxMode: 0 | 1 }>) {
    taxMode = event.detail.taxMode;
    // Recalculate all totals when tax mode changes
    calculateTotals();
    updateFormChanged();
  }

  // Email functionality for readonly mode
  async function handleExportPDF() {
    if (!formData) return;

    try {
      console.log('Generating PDF with actual invoice data...');
      await generateInvoicePDFFromLayout(formData);
      
      addToast({
        message: 'PDF exported successfully',
        type: 'success'
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      addToast({
        message: 'Failed to export PDF: ' + (error instanceof Error ? error.message : 'Unknown error'),
        type: 'error'
      });
    }
  }

  async function handleSendEmail() {
    if (!formData || !emailAddress.trim()) {
      addToast({
        message: 'Please enter a valid email address',
        type: 'error'
      });
      return;
    }

    isSendingEmail = true;

    try {
      // Generate PDF as attachment
      const pdfBlob = await generateInvoicePDFAsBlob(formData);
      const pdfBase64 = await blobToBase64(pdfBlob);

      const pdfAttachment: EmailAttachment = {
        filename: `${formData.invoiceNumber}.pdf`,
        content: pdfBase64,
        contentType: 'application/pdf'
      };

      // Send the email
      const invoiceTotal = formData.invoiceLines.reduce((sum, line) => sum + line.total, 0);
      const response = await sendInvoiceEmail(
        formData.id || '',
        emailAddress,
        'Customer', // We'll need to get customer name from somewhere
        formData.invoiceNumber?.toString() || 'Draft',
        invoiceTotal,
        pdfAttachment
      );

      if (response.success) {
        addToast({
          message: 'Invoice sent successfully',
          type: 'success'
        });

        // Refresh email history
        emailHistory = await getEmailHistoryForInvoice(formData.id || '');

        // Close modal
        showEmailModal = false;

        // Reset form
        emailAddress = '';
        emailMessage = '';
      } else {
        throw new Error(response.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      addToast({
        message: error instanceof Error ? error.message : 'Failed to send email',
        type: 'error'
      });
    } finally {
      isSendingEmail = false;
    }
  }

  // Helper function to convert blob to base64
  function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // Helper function to generate PDF as blob
  async function generateInvoicePDFAsBlob(invoice: any): Promise<Blob> {
    // This is a placeholder - you'll need to implement this based on your PDF generation library
    // For now, we'll use the existing PDF generation and convert to blob
    await generateInvoicePDFFromLayout(invoice);

    // Return a dummy blob for now - replace with actual PDF blob generation
    return new Blob(['PDF content'], { type: 'application/pdf' });
  }

  function openEmailModal() {
    showEmailModal = true;
    // Set default email address if available
    // Note: We'll need to get customer email from somewhere
  }

  // Get page title based on mode
  $: pageTitle = isNewInvoice ? 'Create Invoice' : isEditMode ? 'Edit Invoice' : 'Invoice Details';
</script>

<svelte:head>
  <title>{pageTitle}</title>
</svelte:head>

<div class="container">
  <PageHeader title={pageTitle}>
    <svelte:fragment slot="actions">
      {#if mode === 'edit'}
        <div class="save-status-container">
          {#if formChanged}
            <div class="save-status unsaved">
              <div class="status-dot"></div>
              Unsaved changes
            </div>
          {:else}
            <div class="save-status saved">
              <div class="status-dot"></div>
              All changes saved
            </div>
          {/if}
        </div>
        <Button variant="secondary" on:click={cancelForm}>Cancel</Button>
        <Button variant="primary" on:click={handleSubmit} disabled={isSaving}>
          {#if isSaving}
            <LoadingSpinner variant="white" message="" />
            Saving...
          {:else}
            {isNewInvoice ? 'Save Invoice' : 'Update Invoice'}
          {/if}
        </Button>
      {:else}
        <Button variant="secondary" on:click={() => goto(`/invoices/${invoiceId}?edit=true`)}>
          Edit Invoice
        </Button>
        <Button variant="secondary" on:click={handleExportPDF}>
          Export PDF
        </Button>
        <Button variant="secondary" on:click={openEmailModal}>
          Send Email
        </Button>
      {/if}
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if isLoading}
      <LoadingSpinner message="Loading invoice..." />
    {:else}
      <div class="editor-content">
        <div class="a4-canvas">

          <!-- Row 1: Invoice Header -->
          <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
            <div class="layout-column">
              <DocumentHeader invoiceNumber={String(formData.invoiceNumber || 'Draft')} />
            </div>
            <div class="layout-column">
              <CompanyLogo />
            </div>
          </div>

          <!-- Row 2: Company and Customer Info -->
          <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
            <div class="layout-column">
              <CustomerDetails {formData} {customerSearch} {formSubmitted} {errors} {mode} on:change={updateFormChanged} />
            </div>
            <div class="layout-column">
              <CompanyDetails />
            </div>
          </div>

          <!-- Row 3: Invoice Details -->
          <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
            <div class="layout-column">
              
            </div>
            <div class="layout-column">
              <HeaderFields {formData} {formSubmitted} {errors} {mode} on:change={updateFormChanged} />
            </div>
          </div>

          <!-- Row 4: Line Items -->
          <div class="layout-row" style="grid-template-columns: 1fr;">
            <div class="layout-column">
                <LineItems
                  bind:lineItems={formData.invoiceLines}
                  {mode}
                  currency="£"
                  {formSubmitted}
                  {errors}
                  {taxMode}
                  showTotals={true}
                  {totals}
                  discountAmount={formData.discountAmount}
                  on:updateLineItem={(event) => updateLineItem(event.detail.index, event.detail.lineItem)}
                  on:addLineItem={addLineItem}
                  on:removeLineItem={(event) => removeLineItem(event.detail.index)}
                  on:calculateTotals={calculateTotals}
                  on:taxModeChange={handleTaxModeChange}
                />
              </div>
          </div>

          <!-- Row 5: Notes and Terms -->
          <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
            <div class="layout-column">
              <InvoiceNotes {formData} {mode} on:change={updateFormChanged} />
            </div>
            <div class="layout-column">
              <InvoiceTerms {formData} {mode} on:change={updateFormChanged} />
            </div>
          </div>

          <!-- Row 6: Footer -->
          <div class="layout-row" style="grid-template-columns: 1fr;">
            <div class="layout-column">
              <FooterFields />
            </div>
          </div>

        </div>
      </div>
    {/if}
  </main>
</div>

<!-- Email Modal -->
{#if showEmailModal}
  <Modal title="Send Invoice" on:close={() => showEmailModal = false}>
    <div class="email-form">
      <div class="form-group">
        <label for="email-address">Email Address</label>
        <input
          id="email-address"
          type="email"
          bind:value={emailAddress}
          placeholder="<EMAIL>"
          required
        />
      </div>

      <div class="form-group">
        <label for="email-subject">Subject</label>
        <input
          id="email-subject"
          type="text"
          bind:value={emailSubject}
          placeholder="Invoice subject"
        />
      </div>

      <div class="form-group">
        <label for="email-message">Message (Optional)</label>
        <textarea
          id="email-message"
          bind:value={emailMessage}
          rows="4"
          placeholder="Additional message to include with the invoice..."
        ></textarea>
      </div>

      {#if emailHistory.length > 0}
        <div class="email-history">
          <h4>Email History</h4>
          <div class="history-list">
            {#each emailHistory as email}
              <div class="history-item">
                <div class="email-info">
                  <strong>{email.to}</strong>
                  <span class="email-date">{new Date(email.sentAt).toLocaleDateString()}</span>
                </div>
                <div class="email-status">
                  <span class="status-badge {email.status.toLowerCase()}">{email.status}</span>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>

    <svelte:fragment slot="footer">
      <Button variant="secondary" on:click={() => showEmailModal = false}>
        Cancel
      </Button>
      <Button variant="primary" on:click={handleSendEmail} disabled={isSendingEmail}>
        {#if isSendingEmail}
          <LoadingSpinner size="small" />
          Sending...
        {:else}
          Send Invoice
        {/if}
      </Button>
    </svelte:fragment>
  </Modal>
{/if}

<style lang="less">

  .editor-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 2rem 0;
  }

  .a4-canvas {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 4rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: grid;
    gap: 1rem;
    max-width: 210mm; // A4 width
    margin: 0 auto;
    box-sizing: border-box;
  }

  .layout-row {
    display: grid;
    gap: 2rem;
    align-items: start;
  }

  .layout-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .save-status-container {
    display: flex;
    align-items: center;
    margin-right: 1rem;
  }

  .save-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--br);

    &.saved {
      color: var(--success);
      background-color: var(--success-bg);

      .status-dot {
        background-color: var(--success);
      }
    }

    &.unsaved {
      color: var(--warning);
      background-color: var(--warning-bg);

      .status-dot {
        background-color: var(--warning);
      }
    }
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  // Email modal styles
  .email-form {
    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--black);
      }

      input, textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;
        font-family: inherit;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }

      textarea {
        resize: vertical;
        min-height: 100px;
      }
    }
  }

  .email-history {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);

    h4 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1rem;
    }

    .history-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .history-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      background: var(--bg);
      border-radius: var(--br);
      border: 1px solid var(--border);

      .email-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        .email-date {
          font-size: 0.8rem;
          color: var(--grey);
        }
      }

      .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: var(--br);
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;

        &.sent {
          background: var(--success-bg);
          color: var(--success);
        }

        &.failed {
          background: var(--error-bg);
          color: var(--error);
        }

        &.pending {
          background: var(--warning-bg);
          color: var(--warning);
        }
      }
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 0.5rem;
    }

    .a4-canvas {
      padding: 1rem;
      margin: 0;
      max-width: none;
    }

    .layout-row {
      grid-template-columns: 1fr !important;
      gap: 1rem;
    }

    .save-status-container {
      margin-right: 0.5rem;
    }
  }
</style>