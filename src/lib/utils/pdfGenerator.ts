import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import type { Invoice, InvoiceLineItem, ApiInvoice } from '$lib/api/invoices';
import { formatCurrency } from '$lib/config/currency';
import { getStatusDisplay } from '$lib/api/invoices';

export interface InvoiceFormData {
  invoiceNumber: string;
  customerId: string;
  issueDate: string;
  dueDate: string;
  reference?: string;
  status: { id: string; name: string; color: string };
  lineItems: InvoiceLineItem[];
  customHeaderFields: Array<{ id: string; label: string; value: string; type: string }>;
  notes: string;
  terms: string;
  templateId: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
}

// PDF page constants
const PAGE_HEIGHT = 297; // A4 height in mm
const TOP_MARGIN = 20;
const BOTTOM_MARGIN = 20;
const MAX_CONTENT_HEIGHT = PAGE_HEIGHT - BOTTOM_MARGIN;

// Helper function to ensure content fits on page
function ensureSpace(pdf: jsPDF, currentY: number, blockHeight: number, leftMargin: number = 20): number {
  // Check if current position plus block height exceeds page height minus bottom margin
  if (currentY + blockHeight > MAX_CONTENT_HEIGHT) {
    // Add new page and reset position to top margin
    pdf.addPage();
    return TOP_MARGIN;
  }
  // Return current position if there's enough space
  return currentY;
}

// Helper function to set font consistently throughout PDF
function setPDFFont(pdf: jsPDF, style: 'normal' | 'bold' = 'normal'): void {
  // jsPDF has limited built-in fonts: 'courier', 'helvetica', 'times'
  // We'll use helvetica as it's the closest to IBM Plex Sans (both are sans-serif)
  // IBM Plex Sans would need to be embedded as a custom font for true support
  pdf.setFont('helvetica', style);
}

// Helper function to check if a font is actually available
function isFontAvailable(fontFamily: string): boolean {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return false;
  
  // Test if the font renders differently than a fallback
  const testText = 'abcdefghijklmnopqrstuvwxyz0********9';
  context.font = '12px monospace';
  const fallbackWidth = context.measureText(testText).width;
  
  context.font = `12px "${fontFamily}", monospace`;
  const fontWidth = context.measureText(testText).width;
  
  return Math.abs(fontWidth - fallbackWidth) > 1; // If widths differ, font is available
}

export async function generateInvoicePDF(
  invoiceData: Invoice | InvoiceFormData,
  customerName?: string,
  customerAddress?: any
): Promise<void> {
  try {
    // Wait for all fonts to load
    await document.fonts.ready;
    
    // Load IBM Plex Sans explicitly and wait for it
    const fontPromises = [];
    
    // Load different weights of IBM Plex Sans
    const ibmPlexSansRegular = new FontFace(
      'IBM Plex Sans',
      'url(https://fonts.gstatic.com/s/ibmplexsans/v19/zYX9KVElMYYaJe8bpLHa9WoBUvPFuPlX.woff2)',
      { weight: '400', style: 'normal' }
    );
    
    const ibmPlexSansBold = new FontFace(
      'IBM Plex Sans',
      'url(https://fonts.gstatic.com/s/ibmplexsans/v19/zYXgKVElMYYaJe8bpLHa9WoBCjAKkQhANg.woff2)',
      { weight: '700', style: 'normal' }
    );

    fontPromises.push(ibmPlexSansRegular.load());
    fontPromises.push(ibmPlexSansBold.load());

    try {
      const loadedFonts = await Promise.all(fontPromises);
      loadedFonts.forEach(font => document.fonts.add(font));
      console.log('IBM Plex Sans fonts loaded successfully');
    } catch (e) {
      console.warn('Could not load IBM Plex Sans fonts, using system fallbacks:', e);
    }
    
    // Additional wait for fonts to settle
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Verify font is actually available
    const fontAvailable = isFontAvailable('IBM Plex Sans');
    console.log(`IBM Plex Sans available: ${fontAvailable}`);

    // Create a temporary container for the invoice HTML
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '794px'; // A4 width in pixels at 96 DPI
    container.style.backgroundColor = 'white';
    container.style.padding = '40px';
    container.style.fontFamily = "'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif";
    container.style.fontSize = '12px';
    container.style.lineHeight = '1.4';
    container.style.color = '#000000';

    // Generate the invoice HTML
    container.innerHTML = generateInvoiceHTML(invoiceData, customerName, customerAddress);

    // Add to DOM temporarily
    document.body.appendChild(container);

    try {
      // Wait longer for fonts to fully render
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Force a reflow to ensure fonts are applied
      container.offsetHeight;
      
      // Convert HTML to canvas
      const canvas = await html2canvas(container, {
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794,
        height: container.scrollHeight,
        logging: false, // Reduce console noise
        onclone: (clonedDoc) => {
          // Ensure fonts are applied in the cloned document
          const clonedContainer = clonedDoc.querySelector('div');
          if (clonedContainer) {
            clonedContainer.style.fontFamily = "'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif";
          }
        }
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'px',
        format: [794, 1123] // A4 size in pixels
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 794;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Add image to PDF
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // Generate filename
      const invoiceNumber = 'invoiceNumber' in invoiceData 
        ? invoiceData.invoiceNumber 
        : `INV-${new Date().getFullYear()}-DRAFT`;
      const filename = `${invoiceNumber}.pdf`;

      // Download the PDF
      pdf.save(filename);
    } finally {
      // Clean up
      document.body.removeChild(container);
    }
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to generate line items HTML for PDF
function generateLineItemsHTML(lineItems: any[]): string {
  // Calculate totals
  const subtotal = lineItems.reduce((sum, item) => sum + (item.quantity * (item.unitPrice || item.price)), 0);
  const taxAmount = lineItems.reduce((sum, item) => sum + (item.quantity * (item.unitPrice || item.price) * (item.taxRate || 0)), 0);
  const discountAmount = 0; // Add discount logic if needed
  const total = subtotal + taxAmount - discountAmount;

  return `
    <!-- Line Items Table -->
    <div style="margin-bottom: 30px;">
      <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: left; font-weight: bold;">Description</th>
            <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: center; font-weight: bold; width: 80px;">Qty</th>
            <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 100px;">Unit Price</th>
            <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 80px;">Tax %</th>
            <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 100px;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${lineItems.map(item => `
            <tr>
              <td style="border: 1px solid #e5e7eb; padding: 12px; vertical-align: top;">
                <div style="font-weight: 500;">${item.description}</div>
                ${item.additionalInfo ? `<div style="font-size: 10px; color: #6b7280; margin-top: 4px;">${item.additionalInfo}</div>` : ''}
              </td>
              <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: center;">${item.quantity}</td>
              <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right;">${formatCurrency(item.unitPrice || item.price)}</td>
              <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right;">${item.taxRate ? (item.taxRate * 100).toFixed(1) : '0.0'}%</td>
              <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: 500;">${formatCurrency(item.total || item.lineTotal)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
    </div>

    <!-- Totals -->
    <div style="display: flex; justify-content: flex-end; margin-bottom: 40px;">
      <div style="
        width: 350px;
        margin-top: 24px;
        padding: 20px;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
      ">
        <div style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e5e7eb;
        ">
          <span style="color: #6b7280; font-weight: 500;">Subtotal:</span>
          <span style="font-weight: 500; color: #000; text-align: right;">${formatCurrency(subtotal)}</span>
        </div>
        <div style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e5e7eb;
        ">
          <span style="color: #6b7280; font-weight: 500;">Tax:</span>
          <span style="font-weight: 500; color: #000; text-align: right;">${formatCurrency(taxAmount)}</span>
        </div>
        ${discountAmount > 0 ? `
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
          ">
            <span style="color: #6b7280; font-weight: 500;">Discount:</span>
            <span style="font-weight: 500; color: #000; text-align: right;">-${formatCurrency(discountAmount)}</span>
          </div>
        ` : ''}
        <div style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0 8px 0;
          border-top: 1px solid #e5e7eb;
          margin-top: 8px;
          font-weight: 600;
          font-size: 1.1em;
          color: #000;
        ">
          <span style="color: #6b7280; font-weight: 500;">Total:</span>
          <span style="font-weight: 500; color: #000; text-align: right;">${formatCurrency(total)}</span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; font-size: 10px; color: #9ca3af;">
      Thank you for your business!
    </div>
  `;
}

function generateInvoiceHTML(
  invoiceData: Invoice | InvoiceFormData,
  customerName?: string,
  customerAddress?: any
): string {
  const isFormData = !('invoiceNumber' in invoiceData);
  const invoiceNumber = isFormData ? 'DRAFT' : invoiceData.invoiceNumber;
  
  // Get customer info
  const custName = customerName || (isFormData ? 'Customer Name' : (invoiceData as Invoice).customerName) || 'Customer Name';
  const custAddress = customerAddress || (isFormData ? null : (invoiceData as Invoice).customerAddress);

  return `
    <div style="max-width: 794px; margin: 0 auto; background: white; padding: 0; font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; color: #000000;">
      
      <!-- Row 1: Invoice Header -->
      <div class="layout-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start; margin-bottom: 2rem;">
        <div class="layout-column">
          ${generateDocumentHeader(invoiceNumber)}
        </div>
        <div class="layout-column">
          ${generateCompanyLogo()}
        </div>
      </div>

      <!-- Row 2: Company and Customer Info -->
      <div class="layout-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start; margin-bottom: 2rem;">
        <div class="layout-column">
          ${generateCustomerDetails(custName, custAddress)}
        </div>
        <div class="layout-column">
          ${generateCompanyDetails()}
        </div>
      </div>

      <!-- Row 3: Invoice Details -->
      <div class="layout-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start; margin-bottom: 2rem;">
        <div class="layout-column">
          ${generateInvoiceNotes(invoiceData.notes)}
        </div>
        <div class="layout-column">
          ${generateHeaderFields(invoiceData)}
        </div>
      </div>

      <!-- Row 4: Line Items -->
      <div class="layout-row" style="display: grid; grid-template-columns: 1fr; margin-bottom: 2rem;">
        <div class="layout-column">
          ${generateLineItemsHTML(invoiceData.lineItems)}
        </div>
      </div>

      <!-- Row 5: Notes and Terms -->
      <div class="layout-row" style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start;">
        <div class="layout-column">
          ${generateBankDetails()}
        </div>
        <div class="layout-column">
          ${generateInvoiceTerms(invoiceData.terms)}
        </div>
      </div>

    </div>
  `;
}

// Component helper functions that mirror the Svelte components

function generateDocumentHeader(invoiceNumber: string): string {
  return `
    <div class="invoice-title-section">
      <h1 style="margin: 0; font-size: 2rem; font-weight: bold; color: #2563eb;">INVOICE</h1>
      <div style="font-size: 1.2rem; color: #6b7280; margin-top: 0.5rem;">#${invoiceNumber}</div>
    </div>
  `;
}

function generateCompanyLogo(): string {
  return `
    <div style="text-align: right;">
      <div style="width: 100px; height: 50px; background-color: #f3f4f6; border: 1px dashed #d1d5db; display: inline-flex; align-items: center; justify-content: center; font-size: 0.75rem; color: #6b7280; margin-bottom: 20px;">
        [Company Logo]
      </div>
    </div>
  `;
}

function generateCustomerDetails(customerName: string, customerAddress?: any): string {
  return `
    <div class="customer-details">
      <h3 style="margin-bottom: 1rem; font-weight: 600; font-size: 1.1rem;">To</h3>
      <div style="background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 4px; padding: 12px; line-height: 1.5;">
        <strong>${customerName}</strong><br>
        ${customerAddress ? `
          ${customerAddress.street}<br>
          ${customerAddress.city}, ${customerAddress.state} ${customerAddress.zipCode}<br>
          ${customerAddress.country}
        ` : `
          123 Customer Street<br>
          City, State 12345<br>
          Phone: (*************<br>
          Email: <EMAIL>
        `}
      </div>
    </div>
  `;
}

function generateCompanyDetails(): string {
  return `
    <div class="company-details">
      <h3 style="margin-bottom: 1rem; font-weight: 600; font-size: 1.1rem;">From</h3>
      <div style="background-color: #f9fafb; border: 1px solid #e5e7eb; border-radius: 4px; padding: 12px; line-height: 1.5;">
        <strong>Your Company Name</strong><br>
        123 Business Street<br>
        City, State 12345<br>
        Phone: (*************<br>
        Email: <EMAIL>
      </div>
    </div>
  `;
}

function generateHeaderFields(invoiceData: any): string {
  const issueDate = new Date(invoiceData.issueDate).toLocaleDateString();
  const dueDate = new Date(invoiceData.dueDate).toLocaleDateString();
  const statusDisplay = getStatusDisplay(invoiceData.status);
  
  return `
    <div class="header-fields">
      <div style="display: flex; flex-direction: column; gap: 0.75rem;">
        <div style="display: flex; justify-content: space-between;">
          <span style="font-weight: 500;">Issue Date:</span>
          <span>${issueDate}</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="font-weight: 500;">Due Date:</span>
          <span>${dueDate}</span>
        </div>
        ${invoiceData.reference ? `
          <div style="display: flex; justify-content: space-between;">
            <span style="font-weight: 500;">Reference:</span>
            <span>${invoiceData.reference}</span>
          </div>
        ` : ''}
        <div style="display: flex; justify-content: space-between;">
          <span style="font-weight: 500;">Status:</span>
          <span style="color: ${statusDisplay.color}; font-weight: 500;">${statusDisplay.name}</span>
        </div>
      </div>
    </div>
  `;
}

function generateInvoiceNotes(notes?: string): string {
  if (!notes) return '<div></div>';
  
  return `
    <div class="invoice-notes">
      <h4 style="margin: 0 0 0.5rem 0; font-weight: 600;">Notes</h4>
      <p style="margin: 0; line-height: 1.5; color: #374151;">${notes}</p>
    </div>
  `;
}

function generateBankDetails(): string {
  return `
    <div class="bank-details">
      <h4 style="margin: 0 0 0.5rem 0; font-weight: 600;">Bank Details</h4>
      <div style="line-height: 1.5; color: #374151;">
        <div>Bank Name: Your Bank</div>
        <div>Account: ********</div>
        <div>Sort Code: 12-34-56</div>
      </div>
    </div>
  `;
}

function generateInvoiceTerms(paymentTerms?: string): string {
  const defaultTerms = paymentTerms || 'Payment is due within 30 days of the invoice date.';
  
  return `
    <div class="invoice-terms">
      <h4 style="margin: 0 0 0.5rem 0; font-weight: 600;">Terms and Conditions</h4>
      <p style="margin: 0; line-height: 1.5; color: #374151;">${defaultTerms}</p>
    </div>
  `;
}

// Updated function for generating PDFs matching the new component-based layout
export async function generateInvoicePDFFromLayout(
  invoice: ApiInvoice | any,
  customerName?: string,
  customerAddress?: any
): Promise<void> {
  try {
    // Create PDF with A4 dimensions
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Set up fonts and colors
    // Note: jsPDF has limited built-in fonts. Using 'helvetica' as fallback for IBM Plex Sans
    // To use IBM Plex Sans, we would need to load it as a custom font
    setPDFFont(pdf, 'normal');
    pdf.setFontSize(12);

    let yPosition = 20;
    const leftMargin = 20;
    const rightMargin = 190;
    const pageWidth = 210;
    const columnWidth = (pageWidth - 40) / 2;

    // Use component-based approach similar to HTML generation
    yPosition = ensureSpace(pdf, yPosition, 30, leftMargin); // Document header space
    yPosition = addDocumentHeaderToPDF(pdf, invoice, leftMargin, rightMargin, yPosition);
    
    yPosition = ensureSpace(pdf, yPosition, 30, leftMargin); // Customer/company details space
    yPosition = addCustomerCompanyDetailsToPDF(pdf, invoice, leftMargin, columnWidth, yPosition, customerName, customerAddress);
    
    yPosition = ensureSpace(pdf, yPosition, 25, leftMargin); // Notes/header fields space
    yPosition = addNotesHeaderFieldsToPDF(pdf, invoice, leftMargin, columnWidth, yPosition);
    
    // Calculate line items table height (header + rows + totals)
    const lineItemsHeight = 25 + (invoice.invoiceLines.length * 12) + 60; // Approximate height
    yPosition = ensureSpace(pdf, yPosition, lineItemsHeight, leftMargin);
    yPosition = addLineItemsToPDF(pdf, invoice, leftMargin, rightMargin, yPosition);
    
    yPosition = ensureSpace(pdf, yPosition, 35, leftMargin); // Bank/terms space
    yPosition = addBankTermsToPDF(pdf, invoice, leftMargin, columnWidth, yPosition);
    
    yPosition = ensureSpace(pdf, yPosition, 15, leftMargin); // Footer space
    addFooterToPDF(pdf, leftMargin, rightMargin, yPosition);

    // Generate filename and save
    const filename = `${invoice.invoiceNumber || 'Draft'}.pdf`;
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Component helper functions for PDF generation

function addDocumentHeaderToPDF(pdf: jsPDF, invoice: any, leftMargin: number, rightMargin: number, yPosition: number): number {
  // Row 1: Invoice Header (DocumentHeader + CompanyLogo)
  // Left side: Document Header
  pdf.setFontSize(24);
  pdf.setTextColor(37, 99, 235); // Primary color
  setPDFFont(pdf, 'bold');
  pdf.text('INVOICE', leftMargin, yPosition);

  pdf.setFontSize(12);
  pdf.setTextColor(0, 0, 0);
  setPDFFont(pdf, 'normal');
  const invoiceNumberStr = String(invoice.invoiceNumber || 'Draft');
  pdf.text(`#${invoiceNumberStr}`, leftMargin, yPosition + 8);

  // Right side: Company Logo placeholder
  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);
  pdf.text('[Company Logo]', rightMargin - 30, yPosition);

  return yPosition + 30;
}

function addCustomerCompanyDetailsToPDF(pdf: jsPDF, invoice: any, leftMargin: number, columnWidth: number, yPosition: number, customerName?: string, customerAddress?: any): number {
  // Row 2: Customer Details + Company Details
  // Left side: Customer Details (To)
  pdf.setFontSize(12);
  pdf.setTextColor(0, 0, 0);
  setPDFFont(pdf, 'bold');
  pdf.text('To', leftMargin, yPosition);

  setPDFFont(pdf, 'normal');
  yPosition += 6;
  const custName = customerName || 'Customer Name';
  pdf.text(custName, leftMargin, yPosition);
  yPosition += 4;
  pdf.text('123 Customer Street', leftMargin, yPosition);
  yPosition += 4;
  pdf.text('City, State 12345', leftMargin, yPosition);
  yPosition += 4;
  pdf.text('Phone: (*************', leftMargin, yPosition);
  yPosition += 4;
  pdf.text('Email: <EMAIL>', leftMargin, yPosition);

  // Right side: Company Details
  const companyYStart = yPosition - 24;
  setPDFFont(pdf, 'bold');
  pdf.text('From', leftMargin + columnWidth, companyYStart);
  setPDFFont(pdf, 'normal');
  pdf.text('Your Company Name', leftMargin + columnWidth, companyYStart + 6);
  pdf.text('123 Business Street', leftMargin + columnWidth, companyYStart + 10);
  pdf.text('City, State 12345', leftMargin + columnWidth, companyYStart + 14);
  pdf.text('Phone: (*************', leftMargin + columnWidth, companyYStart + 18);
  pdf.text('Email: <EMAIL>', leftMargin + columnWidth, companyYStart + 22);

  return yPosition + 20;
}

function addNotesHeaderFieldsToPDF(pdf: jsPDF, invoice: any, leftMargin: number, columnWidth: number, yPosition: number): number {
  // Row 3: Notes + Header Fields (Invoice Details)
  // Left side: Notes
  if (invoice.notes) {
    // Calculate approximate height needed for notes
    const notesLines = pdf.splitTextToSize(invoice.notes, columnWidth - 10);
    const notesHeight = notesLines.length * 4 + 10; // Approximate line height
    yPosition = ensureSpace(pdf, yPosition, notesHeight, leftMargin);
    
    setPDFFont(pdf, 'bold');
    pdf.text('Notes', leftMargin, yPosition);
    setPDFFont(pdf, 'normal');
    yPosition += 6;
    pdf.text(notesLines, leftMargin, yPosition);
  }

  // Right side: Header Fields (Invoice Details)
  const headerYStart = yPosition - (invoice.notes ? 14 : 0);
  setPDFFont(pdf, 'bold');

  // Issue Date
  const issueDateStr = invoice.issueDate ? new Date(invoice.issueDate).toLocaleDateString() : 'N/A';
  pdf.text('Issue Date:', leftMargin + columnWidth, headerYStart);
  setPDFFont(pdf, 'normal');
  pdf.text(issueDateStr, leftMargin + columnWidth + 25, headerYStart);

  // Due Date
  const dueDateStr = invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : 'N/A';
  setPDFFont(pdf, 'bold');
  pdf.text('Due Date:', leftMargin + columnWidth, headerYStart + 6);
  setPDFFont(pdf, 'normal');
  pdf.text(dueDateStr, leftMargin + columnWidth + 25, headerYStart + 6);

  let currentFieldY = headerYStart + 12;

  // Reference (if provided)
  if (invoice.reference) {
    setPDFFont(pdf, 'bold');
    pdf.text('Reference:', leftMargin + columnWidth, currentFieldY);
    setPDFFont(pdf, 'normal');
    pdf.text(invoice.reference, leftMargin + columnWidth + 25, currentFieldY);
    currentFieldY += 6;
  }

  // Status
  const statusDisplay = getStatusDisplay(invoice.status);
  setPDFFont(pdf, 'bold');
  pdf.text('Status:', leftMargin + columnWidth, currentFieldY);
  pdf.setTextColor(statusDisplay.color);
  setPDFFont(pdf, 'normal');
  pdf.text(statusDisplay.name, leftMargin + columnWidth + 25, currentFieldY);
  pdf.setTextColor(0, 0, 0); // Reset to black

  return yPosition + 20;
}

function addLineItemsToPDF(pdf: jsPDF, invoice: any, leftMargin: number, rightMargin: number, yPosition: number): number {
  // Row 4: Line Items Table
  yPosition += 10;

  // Table headers with borders
  const tableHeaders = ['Description', 'Qty', 'Unit Price', 'Tax %', 'Total'];
  const columnWidths = [80, 20, 30, 25, 30];
  let xPosition = leftMargin;

  // Header background
  pdf.setFillColor(243, 244, 246); // Light grey background
  pdf.rect(leftMargin, yPosition - 5, rightMargin - leftMargin, 12, 'F');

  // Header borders
  pdf.setDrawColor(229, 231, 235); // Border color
  pdf.setLineWidth(0.1);

  // Draw table header borders
  let currentX = leftMargin;
  for (let i = 0; i <= columnWidths.length; i++) {
    pdf.line(currentX, yPosition - 5, currentX, yPosition + 7);
    if (i < columnWidths.length) {
      currentX += columnWidths[i];
    }
  }
  pdf.line(leftMargin, yPosition - 5, rightMargin, yPosition - 5); // Top border
  pdf.line(leftMargin, yPosition + 7, rightMargin, yPosition + 7); // Bottom border

  // Header text
  setPDFFont(pdf, 'bold');
  pdf.setFontSize(12);
  pdf.setTextColor(0, 0, 0);
  xPosition = leftMargin;
  tableHeaders.forEach((header, index) => {
    const textAlign = index === 0 ? 'left' : index === 1 ? 'center' : 'right';
    if (textAlign === 'center') {
      pdf.text(header, xPosition + columnWidths[index] / 2, yPosition + 2, { align: 'center' });
    } else if (textAlign === 'right') {
      pdf.text(header, xPosition + columnWidths[index] - 2, yPosition + 2, { align: 'right' });
    } else {
      pdf.text(header, xPosition + 2, yPosition + 2);
    }
    xPosition += columnWidths[index];
  });

  yPosition += 12;
  setPDFFont(pdf, 'normal'); // Reset font for content

  // Table rows with borders
  setPDFFont(pdf, 'normal');
  pdf.setFontSize(12);

  invoice.invoiceLines.forEach((item: any, index: number) => {
    const rowHeight = 12;

    // Check if we need a new page for this row
    yPosition = ensureSpace(pdf, yPosition, rowHeight, leftMargin);

    // If we're on a new page, we might need to redraw table headers
    if (yPosition === TOP_MARGIN && index > 0) {
      // Redraw table headers on new page
      pdf.setFillColor(243, 244, 246); // Light grey background
      pdf.rect(leftMargin, yPosition - 5, rightMargin - leftMargin, 12, 'F');

      // Header borders
      pdf.setDrawColor(229, 231, 235); // Border color
      pdf.setLineWidth(0.1);

      // Draw table header borders
      let currentX = leftMargin;
      for (let i = 0; i <= columnWidths.length; i++) {
        pdf.line(currentX, yPosition - 5, currentX, yPosition + 7);
        if (i < columnWidths.length) {
          currentX += columnWidths[i];
        }
      }
      pdf.line(leftMargin, yPosition - 5, rightMargin, yPosition - 5); // Top border
      pdf.line(leftMargin, yPosition + 7, rightMargin, yPosition + 7); // Bottom border

      // Header text
      setPDFFont(pdf, 'bold');
      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0);
      let headerXPosition = leftMargin;
      tableHeaders.forEach((header, headerIndex) => {
        const textAlign = headerIndex === 0 ? 'left' : headerIndex === 1 ? 'center' : 'right';
        if (textAlign === 'center') {
          pdf.text(header, headerXPosition + columnWidths[headerIndex] / 2, yPosition + 2, { align: 'center' });
        } else if (textAlign === 'right') {
          pdf.text(header, headerXPosition + columnWidths[headerIndex] - 2, yPosition + 2, { align: 'right' });
        } else {
          pdf.text(header, headerXPosition + 2, yPosition + 2);
        }
        headerXPosition += columnWidths[headerIndex];
      });

      yPosition += 12;
      setPDFFont(pdf, 'normal'); // Reset font for content
    }

    // Draw row borders
    pdf.setDrawColor(229, 231, 235);
    pdf.setLineWidth(0.1);

    // Vertical borders
    let currentX = leftMargin;
    for (let i = 0; i <= columnWidths.length; i++) {
      pdf.line(currentX, yPosition, currentX, yPosition + rowHeight);
      if (i < columnWidths.length) {
        currentX += columnWidths[i];
      }
    }
    // Bottom border
    pdf.line(leftMargin, yPosition + rowHeight, rightMargin, yPosition + rowHeight);

    xPosition = leftMargin;

    // Description
    const descriptionText = item.description.length > 35 ? item.description.substring(0, 35) + '...' : item.description;
    setPDFFont(pdf, 'normal');
    pdf.text(descriptionText, xPosition + 2, yPosition + 8);
    xPosition += columnWidths[0];

    // Quantity (centered)
    pdf.text(item.quantity.toString(), xPosition + columnWidths[1] / 2, yPosition + 8, { align: 'center' });
    xPosition += columnWidths[1];

    // Unit Price (right aligned)
    pdf.text(formatCurrency(item.unitPrice), xPosition + columnWidths[2] - 2, yPosition + 8, { align: 'right' });
    xPosition += columnWidths[2];

    // Tax Rate (right aligned)
    pdf.text(`${(item.taxRate * 100).toFixed(1)}%`, xPosition + columnWidths[3] - 2, yPosition + 8, { align: 'right' });
    xPosition += columnWidths[3];

    // Total (right aligned, bold)
    setPDFFont(pdf, 'bold');
    pdf.text(formatCurrency(item.total), xPosition + columnWidths[4] - 2, yPosition + 8, { align: 'right' });

    yPosition += rowHeight;
  });

  yPosition += 20;

  // Ensure space for totals section (approximately 60mm height)
  yPosition = ensureSpace(pdf, yPosition, 60, leftMargin);

  // Totals section (matching template style)
  const totalsWidth = 75;
  const totalsX = rightMargin - totalsWidth;

  // Calculate totals
  const subtotal = invoice.invoiceLines.reduce((sum: number, line: any) => sum + (line.quantity * line.unitPrice), 0);
  const taxAmount = invoice.invoiceLines.reduce((sum: number, line: any) => sum + (line.quantity * line.unitPrice * line.taxRate), 0);
  const discountAmount = 0; // Add discount logic if needed
  const total = subtotal + taxAmount - discountAmount;

  setPDFFont(pdf, 'normal');
  pdf.setFontSize(12);

  // Subtotal
  pdf.text('Subtotal:', totalsX - 5, yPosition, { align: 'right' });
  pdf.text(formatCurrency(subtotal), rightMargin, yPosition, { align: 'right' });
  pdf.setDrawColor(229, 231, 235);
  pdf.line(totalsX - 10, yPosition + 2, rightMargin, yPosition + 2);
  yPosition += 8;

  // Tax
  pdf.text('Tax:', totalsX - 5, yPosition, { align: 'right' });
  pdf.text(formatCurrency(taxAmount), rightMargin, yPosition, { align: 'right' });
  pdf.line(totalsX - 10, yPosition + 2, rightMargin, yPosition + 2);
  yPosition += 8;

  // Discount (if applicable)
  if (discountAmount > 0) {
    pdf.setTextColor(220, 38, 38); // Red color for discount
    pdf.text('Discount:', totalsX - 5, yPosition, { align: 'right' });
    pdf.text(`-${formatCurrency(discountAmount)}`, rightMargin, yPosition, { align: 'right' });
    pdf.line(totalsX - 10, yPosition + 2, rightMargin, yPosition + 2);
    yPosition += 8;
    pdf.setTextColor(0, 0, 0); // Reset to black
  }

  // Total
  pdf.setDrawColor(37, 99, 235); // Primary color
  pdf.setLineWidth(0.5);
  pdf.line(totalsX - 10, yPosition, rightMargin, yPosition);
  yPosition += 5;

  setPDFFont(pdf, 'bold');
  pdf.setFontSize(16);
  pdf.setTextColor(37, 99, 235); // Primary color
  pdf.text('Total:', totalsX - 5, yPosition, { align: 'right' });
  pdf.text(formatCurrency(total), rightMargin, yPosition, { align: 'right' });

  return yPosition + 20;
}

function addBankTermsToPDF(pdf: jsPDF, invoice: any, leftMargin: number, columnWidth: number, yPosition: number): number {
  // Row 5: Bank Details + Terms and Conditions
  const bankYStart = yPosition;

  // Left side: Bank Details
  setPDFFont(pdf, 'bold');
  pdf.setTextColor(0, 0, 0);
  pdf.text('Bank Details', leftMargin, bankYStart);
  setPDFFont(pdf, 'normal');
  pdf.text('Bank Name: Your Bank', leftMargin, bankYStart + 6);
  pdf.text('Account: ********', leftMargin, bankYStart + 10);
  pdf.text('Sort Code: 12-34-56', leftMargin, bankYStart + 14);

  // Right side: Terms and Conditions
  if (invoice.paymentTerms) {
    // Calculate approximate height needed for terms
    const termsLines = pdf.splitTextToSize(invoice.paymentTerms, columnWidth - 10);
    const termsHeight = termsLines.length * 4 + 10; // Approximate line height
    
    // Check if we need more space for terms (but don't move the whole section)
    if (bankYStart + termsHeight > MAX_CONTENT_HEIGHT) {
      // If terms are too long, we might need to continue on next page
      // For now, just ensure we have some space
      yPosition = ensureSpace(pdf, yPosition, Math.min(termsHeight, 30), leftMargin);
    }
    
    setPDFFont(pdf, 'bold');
    pdf.text('Terms and Conditions', leftMargin + columnWidth, bankYStart);
    setPDFFont(pdf, 'normal');
    pdf.text(termsLines, leftMargin + columnWidth, bankYStart + 6);
  }

  return yPosition + 30;
}

function addFooterToPDF(pdf: jsPDF, leftMargin: number, rightMargin: number, yPosition: number): void {
  // Footer
  yPosition += 30;
  pdf.setDrawColor(229, 231, 235);
  pdf.setLineWidth(0.1);
  pdf.line(leftMargin, yPosition, rightMargin, yPosition);
  yPosition += 10;

  setPDFFont(pdf, 'normal');
  pdf.setFontSize(10);
  pdf.setTextColor(156, 163, 175); // Light grey color
  const footerText = 'Thank you for your business!';
  const footerX = leftMargin + (rightMargin - leftMargin) / 2;
  pdf.text(footerText, footerX, yPosition, { align: 'center' });
}