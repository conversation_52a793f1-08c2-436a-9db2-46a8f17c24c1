import type { ApiInvoice } from '$lib/api/invoices';

// Simple function to generate PDF using Puppeteer via API endpoint
export async function generateInvoicePDFFromLayout(invoice: ApiInvoice): Promise<void> {
  try {
    // Call the API endpoint with the invoice data
    const response = await fetch('/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ invoice }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || `HTTP error! status: ${response.status}`);
    }

    // Get the PDF blob from the response
    const blob = await response.blob();
    
    // Create a download link and trigger download
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `invoice-${invoice.invoiceNumber || 'draft'}.pdf`;
    
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Legacy interface for compatibility (no longer used)
export interface InvoiceFormData {
  invoiceNumber: string;
  customerId: string;
  issueDate: string;
  dueDate: string;
  reference?: string;
  status: { id: string; name: string; color: string };
  lineItems: any[];
  customHeaderFields: Array<{ id: string; label: string; value: string; type: string }>;
  notes: string;
  terms: string;
  templateId: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
}

