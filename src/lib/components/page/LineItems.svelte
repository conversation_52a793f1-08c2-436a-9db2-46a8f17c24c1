<!-- LineItems.svelte -->
<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import type { ApiInvoiceLine } from '$lib/api/invoices';
  import { getProducts, type Product } from '$lib/api/invoices';
  import { onMount } from 'svelte';
    import IconBin from '../icons/IconBin.svelte';
    import IconDrag from '../icons/IconDrag.svelte';
    import BankDetails from './BankDetails.svelte';

  // Extended interface to support additional info
  interface ExtendedApiInvoiceLine extends ApiInvoiceLine {
    additionalInfo?: string;
  }

  // Props
  export let lineItems: ExtendedApiInvoiceLine[] = [];
  export let mode: 'edit' | 'readonly' = 'readonly';
  export let currency = '£';
  export let formSubmitted = false;
  export let errors: Record<string, string> = {};
  export let showAddButton = true;
  export let showRemoveButtons = true;
  export let taxMode: 0 | 1 = 0; // 0 = Tax Exclusive, 1 = Tax Inclusive
  export let showTotals = true;
  export let totals = {
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0
  };
  export let discountAmount = 0;

  // Local state
  let products: Product[] = [];
  let searchTerms: string[] = [];
  let showDropdowns: boolean[] = [];
  let showAdditionalInfo: boolean[] = [];
  let filteredProducts: Product[][] = [];
  let editingLineTotals: Set<number> = new Set(); // Track which line totals are being edited

  // Track raw input values for unit price and line total (before 2dp rounding)
  let rawUnitPrices: string[] = [];
  let rawLineTotals: string[] = [];
  let rawTaxRates: string[] = []; // Track raw tax rate input (as percentage)

  // Drag and drop state
  let draggedIndex: number | null = null;
  let dragOverIndex: number | null = null;
  let isDragging = false;
  let dragStartY = 0;
  let initialMouseY = 0;
  let tableElement: HTMLElement;

  // Event dispatcher
  const dispatch = createEventDispatcher<{
    updateLineItem: { index: number; lineItem: ExtendedApiInvoiceLine };
    addLineItem: void;
    removeLineItem: { index: number };
    calculateTotals: void;
    taxModeChange: { taxMode: 0 | 1 };
    reorderLineItems: { oldIndex: number; newIndex: number };
  }>();

  // Initialize arrays when lineItems change
  $: initializeArrays(lineItems);

  function initializeArrays(items: ExtendedApiInvoiceLine[]) {
    if (items && items.length > 0) {
      // Ensure arrays are the correct length
      const targetLength = items.length;
      
      if (searchTerms.length !== targetLength) {
        searchTerms = items.map((item, i) => searchTerms[i] || item.description || '');
      }
      if (showDropdowns.length !== targetLength) {
        showDropdowns = new Array(targetLength).fill(false);
      }
      if (showAdditionalInfo.length !== targetLength) {
        showAdditionalInfo = items.map((item, i) => showAdditionalInfo[i] !== undefined ? showAdditionalInfo[i] : !!item.additionalInfo);
      }
      if (filteredProducts.length !== targetLength) {
        filteredProducts = new Array(targetLength).fill([]).map(() => []);
      }
      if (rawUnitPrices.length !== targetLength) {
        rawUnitPrices = items.map((item, i) => rawUnitPrices[i] || item.unitPrice.toString());
      }
      if (rawLineTotals.length !== targetLength) {
        rawLineTotals = items.map((item, i) => rawLineTotals[i] || item.total.toString());
      }
      if (rawTaxRates.length !== targetLength) {
        rawTaxRates = items.map((item, i) => rawTaxRates[i] || (item.taxRate * 100).toString());
      }
      
      // Only update lineItems if they're not being edited
      items.forEach((item, index) => {
        if (!editingLineTotals.has(index)) {
          lineItems[index] = item;
        }
      });
    }
  }

  onMount(async () => {
    try {
      products = await getProducts();
    } catch (error) {
      console.error('Failed to load products:', error);
      products = [];
    }
  });

  // Handle input changes
  function handleInputChange(index: number, field: keyof ExtendedApiInvoiceLine, value: any) {
    const updatedItem = { ...lineItems[index], [field]: value };
    dispatch('updateLineItem', { index, lineItem: updatedItem });
    dispatch('calculateTotals');
  }

  // Handle add line item
  function handleAddLineItem() {
    dispatch('addLineItem');
  }

  // Handle remove line item
  function handleRemoveLineItem(index: number) {
    dispatch('removeLineItem', { index });
  }

  // Format currency
  function formatCurrency(amount: number): string {
    return `${currency}${amount.toFixed(2)}`;
  }

  // Handle description search
  function handleDescriptionSearch(index: number, event: Event) {
    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;
    searchTerms[index] = searchTerm;
    
    // Filter products based on search term
    if (searchTerm.trim()) {
      const filtered = products.filter(product =>
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      filteredProducts[index] = filtered;
      showDropdowns[index] = true;
    } else {
      filteredProducts[index] = [];
      showDropdowns[index] = false;
    }
    
    // Update the line item description
    handleInputChange(index, 'description', searchTerm);
  }

  // Select a product from dropdown
  function selectProduct(index: number, product: Product | null) {
    if (product) {
      // Update with product data
      const updatedItem = {
        ...lineItems[index],
        description: product.description,
        unitPrice: product.price || 0,
        taxRate: (product.taxRate || 0) / 100 // Convert percentage to decimal
      };
      dispatch('updateLineItem', { index, lineItem: updatedItem });
      searchTerms[index] = product.description;
    } else {
      // Custom item selected - keep current description
      searchTerms[index] = lineItems[index].description || '';
    }
    
    showDropdowns[index] = false;
    dispatch('calculateTotals');
  }

  // Toggle additional info
  function toggleAdditionalInfo(index: number) {
    showAdditionalInfo[index] = !showAdditionalInfo[index];
    if (!showAdditionalInfo[index]) {
      // Clear additional info when hiding
      handleInputChange(index, 'additionalInfo', '');
    }
  }

  // Handle additional info change
  function handleAdditionalInfoChange(index: number, event: Event) {
    const target = event.target as HTMLTextAreaElement;
    handleInputChange(index, 'additionalInfo', target.value);
  }

  // Type-safe event handlers for other fields
  function handleQuantityChange(index: number, event: Event) {
    const target = event.target as HTMLInputElement;
    handleInputChange(index, 'quantity', parseFloat(target.value) || 0);
  }

  function handleUnitPriceChange(index: number, event: Event) {
    const target = event.target as HTMLInputElement;
    const rawValue = target.value;
    
    // Update raw value immediately
    rawUnitPrices[index] = rawValue;
    rawUnitPrices = [...rawUnitPrices];
    
    const newUnitPrice = parseFloat(rawValue) || 0;
    
    if (taxMode === 0) {
      // Tax Exclusive: unit price is editable, calculate line total
      handleInputChange(index, 'unitPrice', newUnitPrice);
    } else {
      // Tax Inclusive: unit price changed, but we need to reverse calculate
      // This shouldn't happen in tax inclusive mode, but handle gracefully
      handleInputChange(index, 'unitPrice', newUnitPrice);
    }
  }

  function handleUnitPriceBlur(index: number) {
    const rawValue = rawUnitPrices[index];
    const numericValue = parseFloat(rawValue) || 0;
    
    // Round to 2 decimal places
    const roundedValue = Math.round(numericValue * 100) / 100;
    
    // Update both raw and actual values
    rawUnitPrices[index] = roundedValue.toString();
    rawUnitPrices = [...rawUnitPrices];
    
    handleInputChange(index, 'unitPrice', roundedValue);
  }

  function handleLineTotalChange(index: number, event: Event) {
    const target = event.target as HTMLInputElement;
    const rawValue = target.value;
    
    // Update raw value immediately
    rawLineTotals[index] = rawValue;
    rawLineTotals = [...rawLineTotals];
    
    const newLineTotal = parseFloat(rawValue) || 0;
    
    // Mark this line as being edited
    editingLineTotals.add(index);
    
    if (taxMode === 1) {
      const quantity = lineItems[index].quantity || 1;
      const taxRate = lineItems[index].taxRate || 0;
      
      // Tax Inclusive: calculate unit price backwards
      const unitPrice = newLineTotal / (quantity * (1 + taxRate));
      const subtotal = unitPrice * quantity;
      const taxAmount = subtotal * taxRate;
      
      // Update locally first
      lineItems[index] = {
        ...lineItems[index],
        unitPrice: unitPrice,
        subtotal: subtotal,
        taxAmount: taxAmount,
        total: newLineTotal
      };
      
      // Also update raw unit price
      rawUnitPrices[index] = unitPrice.toString();
      rawUnitPrices = [...rawUnitPrices];
      
      // Trigger reactivity
      lineItems = [...lineItems];
    }
  }

  function handleLineTotalBlur(index: number) {
    const rawValue = rawLineTotals[index];
    const numericValue = parseFloat(rawValue) || 0;
    
    // Round to 2 decimal places
    const roundedValue = Math.round(numericValue * 100) / 100;
    
    // Update raw value
    rawLineTotals[index] = roundedValue.toString();
    rawLineTotals = [...rawLineTotals];
    
    // When user finishes editing, dispatch the update
    editingLineTotals.delete(index);
    
    // Update the line item with rounded value
    if (taxMode === 1) {
      const quantity = lineItems[index].quantity || 1;
      const taxRate = lineItems[index].taxRate || 0;
      
      // Tax Inclusive: calculate unit price backwards with rounded total
      const unitPrice = roundedValue / (quantity * (1 + taxRate));
      const subtotal = unitPrice * quantity;
      const taxAmount = subtotal * taxRate;
      
      lineItems[index] = {
        ...lineItems[index],
        unitPrice: unitPrice,
        subtotal: subtotal,
        taxAmount: taxAmount,
        total: roundedValue
      };
      
      // Update raw unit price as well
      rawUnitPrices[index] = unitPrice.toString();
      rawUnitPrices = [...rawUnitPrices];
      
      lineItems = [...lineItems];
    }
    
    dispatch('updateLineItem', { index, lineItem: lineItems[index] });
  }

  function handleTaxRateChange(index: number, event: Event) {
    const target = event.target as HTMLInputElement;
    const rawValue = target.value;
    
    // Update raw value immediately
    rawTaxRates[index] = rawValue;
    rawTaxRates = [...rawTaxRates];
    
    const percentageValue = parseFloat(rawValue) || 0;
    // Convert percentage input to decimal for storage
    handleInputChange(index, 'taxRate', percentageValue / 100 || 0);
  }

  function handleTaxRateBlur(index: number) {
    const rawValue = rawTaxRates[index];
    const numericValue = parseFloat(rawValue) || 0;
    
    // Round to 2 decimal places
    const roundedValue = Math.round(numericValue * 100) / 100;
    
    // Update both raw and actual values
    rawTaxRates[index] = roundedValue.toString();
    rawTaxRates = [...rawTaxRates];
    
    // Convert percentage to decimal for storage
    handleInputChange(index, 'taxRate', roundedValue / 100);
  }

  // Close dropdown when clicking outside
  function handleDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.description-autocomplete')) {
      showDropdowns = showDropdowns.map(() => false);
      // Trigger reactivity
      showDropdowns = [...showDropdowns];
    }
  }

  onMount(() => {
    document.addEventListener('click', handleDocumentClick);
    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  });

  function handleTaxModeChange() {
    dispatch('taxModeChange', { taxMode });
    // Recalculate all line items when tax mode changes
    dispatch('calculateTotals');
  }

  // Drag and drop functions
  function handleDragStart(index: number, event: MouseEvent) {
    if (lineItems.length <= 1) return;
    
    draggedIndex = index;
    isDragging = true;
    initialMouseY = event.clientY;
    dragStartY = event.clientY;
    
    // Prevent text selection
    event.preventDefault();
    
    // Add global mouse event listeners
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
    
    // Add dragging class to body to change cursor
    document.body.style.cursor = 'grabbing';
    document.body.style.userSelect = 'none';
  }

  function handleDragMove(event: MouseEvent) {
    if (!isDragging || draggedIndex === null) return;
    
    const currentY = event.clientY;
    const tableRect = tableElement.getBoundingClientRect();
    
    // Constrain to table boundaries
    if (currentY < tableRect.top || currentY > tableRect.bottom) {
      return;
    }
    
    // Find which row we're over
    const rows = tableElement.querySelectorAll('.items-row:not(.dragged)');
    let newOverIndex = draggedIndex;
    
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i] as HTMLElement;
      const rect = row.getBoundingClientRect();
      const rowMiddle = rect.top + rect.height / 2;
      
      if (currentY < rowMiddle) {
        newOverIndex = i >= draggedIndex ? i : i;
        break;
      } else if (i === rows.length - 1) {
        newOverIndex = i >= draggedIndex ? i + 1 : i + 1;
      }
    }
    
    // Adjust for dragged item
    if (newOverIndex > draggedIndex) {
      newOverIndex = Math.min(newOverIndex, lineItems.length - 1);
    }
    
    dragOverIndex = newOverIndex;
  }

  function handleDragEnd() {
    if (!isDragging || draggedIndex === null) return;
    
    // Remove global listeners
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    
    // Reset cursor and selection
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    
    // Perform the reorder if indices are different
    if (dragOverIndex !== null && dragOverIndex !== draggedIndex) {
      // Reorder the items locally for immediate visual feedback
      const item = lineItems.splice(draggedIndex, 1)[0];
      lineItems.splice(dragOverIndex, 0, item);
      lineItems = [...lineItems]; // Trigger reactivity
      
      // Also reorder related arrays to keep them in sync
      const searchTerm = searchTerms.splice(draggedIndex, 1)[0];
      searchTerms.splice(dragOverIndex, 0, searchTerm);
      searchTerms = [...searchTerms];
      
      const showDropdown = showDropdowns.splice(draggedIndex, 1)[0];
      showDropdowns.splice(dragOverIndex, 0, showDropdown);
      showDropdowns = [...showDropdowns];
      
      const showAdditional = showAdditionalInfo.splice(draggedIndex, 1)[0];
      showAdditionalInfo.splice(dragOverIndex, 0, showAdditional);
      showAdditionalInfo = [...showAdditionalInfo];
      
      const filteredProduct = filteredProducts.splice(draggedIndex, 1)[0];
      filteredProducts.splice(dragOverIndex, 0, filteredProduct);
      filteredProducts = [...filteredProducts];
      
      // Reorder raw value arrays
      const rawUnitPrice = rawUnitPrices.splice(draggedIndex, 1)[0];
      rawUnitPrices.splice(dragOverIndex, 0, rawUnitPrice);
      rawUnitPrices = [...rawUnitPrices];
      
      const rawLineTotal = rawLineTotals.splice(draggedIndex, 1)[0];
      rawLineTotals.splice(dragOverIndex, 0, rawLineTotal);
      rawLineTotals = [...rawLineTotals];
      
      const rawTaxRate = rawTaxRates.splice(draggedIndex, 1)[0];
      rawTaxRates.splice(dragOverIndex, 0, rawTaxRate);
      rawTaxRates = [...rawTaxRates];
      
      // Dispatch event to notify parent of the reordering
      dispatch('reorderLineItems', { 
        oldIndex: draggedIndex, 
        newIndex: dragOverIndex 
      });
      
      // Recalculate totals after reordering
      dispatch('calculateTotals');
    }
    
    // Reset drag state
    isDragging = false;
    draggedIndex = null;
    dragOverIndex = null;
  }

  // Keyboard support for drag handle
  function handleKeydown(index: number, event: KeyboardEvent) {
    if (lineItems.length <= 1) return;
    
    if (event.key === 'ArrowUp' && index > 0) {
      event.preventDefault();
      
      // Reorder locally
      const item = lineItems.splice(index, 1)[0];
      lineItems.splice(index - 1, 0, item);
      lineItems = [...lineItems];
      
      // Reorder related arrays
      const searchTerm = searchTerms.splice(index, 1)[0];
      searchTerms.splice(index - 1, 0, searchTerm);
      searchTerms = [...searchTerms];
      
      const showDropdown = showDropdowns.splice(index, 1)[0];
      showDropdowns.splice(index - 1, 0, showDropdown);
      showDropdowns = [...showDropdowns];
      
      const showAdditional = showAdditionalInfo.splice(index, 1)[0];
      showAdditionalInfo.splice(index - 1, 0, showAdditional);
      showAdditionalInfo = [...showAdditionalInfo];
      
      const filteredProduct = filteredProducts.splice(index, 1)[0];
      filteredProducts.splice(index - 1, 0, filteredProduct);
      filteredProducts = [...filteredProducts];
      
      // Reorder raw value arrays
      const rawUnitPrice = rawUnitPrices.splice(index, 1)[0];
      rawUnitPrices.splice(index - 1, 0, rawUnitPrice);
      rawUnitPrices = [...rawUnitPrices];
      
      const rawLineTotal = rawLineTotals.splice(index, 1)[0];
      rawLineTotals.splice(index - 1, 0, rawLineTotal);
      rawLineTotals = [...rawLineTotals];
      
      const rawTaxRate = rawTaxRates.splice(index, 1)[0];
      rawTaxRates.splice(index - 1, 0, rawTaxRate);
      rawTaxRates = [...rawTaxRates];
      
      dispatch('reorderLineItems', { 
        oldIndex: index, 
        newIndex: index - 1 
      });
      dispatch('calculateTotals');
    } else if (event.key === 'ArrowDown' && index < lineItems.length - 1) {
      event.preventDefault();
      
      // Reorder locally
      const item = lineItems.splice(index, 1)[0];
      lineItems.splice(index + 1, 0, item);
      lineItems = [...lineItems];
      
      // Reorder related arrays
      const searchTerm = searchTerms.splice(index, 1)[0];
      searchTerms.splice(index + 1, 0, searchTerm);
      searchTerms = [...searchTerms];
      
      const showDropdown = showDropdowns.splice(index, 1)[0];
      showDropdowns.splice(index + 1, 0, showDropdown);
      showDropdowns = [...showDropdowns];
      
      const showAdditional = showAdditionalInfo.splice(index, 1)[0];
      showAdditionalInfo.splice(index + 1, 0, showAdditional);
      showAdditionalInfo = [...showAdditionalInfo];
      
      const filteredProduct = filteredProducts.splice(index, 1)[0];
      filteredProducts.splice(index + 1, 0, filteredProduct);
      filteredProducts = [...filteredProducts];
      
      // Reorder raw value arrays
      const rawUnitPrice = rawUnitPrices.splice(index, 1)[0];
      rawUnitPrices.splice(index + 1, 0, rawUnitPrice);
      rawUnitPrices = [...rawUnitPrices];
      
      const rawLineTotal = rawLineTotals.splice(index, 1)[0];
      rawLineTotals.splice(index + 1, 0, rawLineTotal);
      rawLineTotals = [...rawLineTotals];
      
      const rawTaxRate = rawTaxRates.splice(index, 1)[0];
      rawTaxRates.splice(index + 1, 0, rawTaxRate);
      rawTaxRates = [...rawTaxRates];
      
      dispatch('reorderLineItems', { 
        oldIndex: index, 
        newIndex: index + 1 
      });
      dispatch('calculateTotals');
    }
  }
</script>

<div class="line-items-section">
  <h3>Line Items

    <div class="tax-mode-selector">
      <label for="tax-mode">Tax Mode:</label>
      <select id="tax-mode" bind:value={taxMode} on:change={handleTaxModeChange}>
        <option value={0}>Tax Exclusive</option>
        <option value={1}>Tax Inclusive</option>
      </select>
    </div>
  </h3>

  

  <div class="line-items-container">
    <div class="items-table" class:readonly={mode === 'readonly'} bind:this={tableElement}>
      <div class="items-header">
        <div class="item-cell">Description</div>
        <div class="item-cell">Quantity</div>
        <div class="item-cell">
          {taxMode === 0 ? 'Unit Price' : 'Unit Price (calc)'}
        </div>
        <div class="item-cell">Tax Rate (%)</div>
        <div class="item-cell line-total">
          {taxMode === 1 ? 'Line Total' : 'Line Total'}
        </div>
      </div>

      {#each lineItems as item, index}
        <div 
          class="items-row" 
          class:dragged={draggedIndex === index}
          class:drag-over={dragOverIndex === index}
          class:drag-active={isDragging}
        >
          {#if mode === 'edit' && lineItems.length > 1}
            <div class="actions left">
              <div 
                class="drag-handle"
                on:mousedown={(e) => handleDragStart(index, e)}
                on:keydown={(e) => handleKeydown(index, e)}
                role="button"
                tabindex="0"
                aria-label="Drag to reorder (use arrow keys or mouse)"
              >
                <IconDrag size="8" />
              </div>
            </div>
          {/if}
          <div class="item-cell">
            {#if mode === 'edit'}
              <div class="item-description">
                <div class="description-autocomplete">
                  <input
                    type="text"
                    value={searchTerms[index] !== undefined ? searchTerms[index] : (item.description || '')}
                    on:input={(e) => handleDescriptionSearch(index, e)}
                    placeholder="Search for item or type custom description..."
                    class:error={formSubmitted && errors[`invoiceLines[${index}].description`]}
                  />
                  
                  {#if showDropdowns[index] && (filteredProducts[index]?.length > 0 || searchTerms[index]?.trim())}
                    <div class="autocomplete-dropdown" role="listbox">
                      <!-- Custom Item option -->
                      <button 
                        type="button"
                        class="dropdown-item custom-item" 
                        on:click={() => selectProduct(index, null)}
                        role="option"
                        aria-selected="false"
                        tabindex="0"
                      >
                        <div class="item-info">
                          <div class="item-name">Custom Item</div>
                          <div class="item-description">Use custom description: "{searchTerms[index] || ''}"</div>
                        </div>
                      </button>
                      
                      <!-- Product suggestions -->
                      {#each (filteredProducts[index] || []) as product}
                        <button 
                          type="button"
                          class="dropdown-item" 
                          on:click={() => selectProduct(index, product)}
                          role="option"
                          aria-selected="false"
                          tabindex="0"
                        >
                          <div class="item-info">
                            <div class="item-name">{product.name || product.description}</div>
                            <div class="item-description">{product.description}</div>
                            <div class="item-price">{formatCurrency(product.price || 0)}</div>
                          </div>
                        </button>
                      {/each}
                      
                      {#if (!filteredProducts[index] || filteredProducts[index].length === 0) && searchTerms[index] && searchTerms[index].trim()}
                        <div class="dropdown-item no-results" role="option" aria-disabled="true">
                          <div class="item-info">
                            <div class="item-name">No matching items found</div>
                            <div class="item-description">Use "Custom Item" to create a new item</div>
                          </div>
                        </div>
                      {/if}
                    </div>
                  {/if}

                  <div class="description-controls">
                    <Button
                      variant="ghost"
                      size="small"
                      on:click={() => toggleAdditionalInfo(index)}
                    >
                      {showAdditionalInfo[index] ? 'Hide' : 'Add'} Details
                    </Button>
                  </div>
                </div>
                
                {#if showAdditionalInfo[index]}
                  <textarea
                    class="additional-info"
                    value={item.additionalInfo || ''}
                    on:input={(e) => handleAdditionalInfoChange(index, e)}
                    placeholder="Additional details, notes, or specifications..."
                    rows="2"
                  ></textarea>
                {/if}
              </div>
              {#if formSubmitted && errors[`invoiceLines[${index}].description`]}
                <div class="error-message">{errors[`invoiceLines[${index}].description`]}</div>
              {/if}
            {:else}
              <div class="description-content">
                <div class="description-main">{item.description}</div>
                {#if item.additionalInfo}
                  <div class="description-additional">{item.additionalInfo}</div>
                {/if}
              </div>
            {/if}
          </div>

          <div class="item-cell">
            {#if mode === 'edit'}
              <input
                type="number"
                value={item.quantity}
                on:input={(e) => handleQuantityChange(index, e)}
                min="1"
                step="1"
                class:error={formSubmitted && errors[`invoiceLines[${index}].quantity`]}
              />
              {#if formSubmitted && errors[`invoiceLines[${index}].quantity`]}
                <div class="error-message">{errors[`invoiceLines[${index}].quantity`]}</div>
              {/if}
            {:else}
              <span class="readonly-value">{item.quantity}</span>
            {/if}
          </div>

          <div class="item-cell">
            {#if mode === 'edit'}
              {#if taxMode === 0}
                <!-- Tax Exclusive: Unit Price is editable -->
                <input
                  type="number"
                  value={rawUnitPrices[index] || item.unitPrice.toString()}
                  on:input={(e) => handleUnitPriceChange(index, e)}
                  on:blur={() => handleUnitPriceBlur(index)}
                  min="0"
                  step="0.01"
                  class:error={formSubmitted && errors[`invoiceLines[${index}].unitPrice`]}
                />
              {:else}
                <!-- Tax Inclusive: Unit Price is read-only -->
                <span class="readonly-value">{formatCurrency(item.unitPrice)}</span>
              {/if}
              {#if formSubmitted && errors[`invoiceLines[${index}].unitPrice`]}
                <div class="error-message">{errors[`invoiceLines[${index}].unitPrice`]}</div>
              {/if}
            {:else}
              <span class="readonly-value">{formatCurrency(item.unitPrice)}</span>
            {/if}
          </div>

          <div class="item-cell">
            {#if mode === 'edit'}
              <input
                type="number"
                value={rawTaxRates[index] || (item.taxRate * 100).toString()}
                on:input={(e) => handleTaxRateChange(index, e)}
                on:blur={() => handleTaxRateBlur(index)}
                min="0"
                max="100"
                step="0.01"
              />
            {:else}
              <span class="readonly-value">{(item.taxRate * 100).toFixed(2)}%</span>
            {/if}
          </div>

          <div class="item-cell amount">
            {#if mode === 'edit' && taxMode === 1}
              <!-- Tax Inclusive: Line Total is editable -->
              <input
                type="number"
                value={rawLineTotals[index] || item.total.toString()}
                on:input={(e) => handleLineTotalChange(index, e)}
                on:blur={() => handleLineTotalBlur(index)}
                min="0"
                step="0.01"
                class="line-total-input"
              />
            {:else}
              <!-- Tax Exclusive or Readonly: Line Total is calculated -->
              {formatCurrency(item.total)}
            {/if}
          </div>

          {#if mode === 'edit'}
            <div class="actions right">
              {#if lineItems.length > 1 && showRemoveButtons}
                <Button
                  variant="ghost"
                  size="small"
                  iconOnly
                  on:click={() => handleRemoveLineItem(index)}
                >
                  <IconBin size="12" />
                </Button>
              {/if}
            </div>
          {/if}
        </div>
      {/each}

      {#if mode === 'edit' && showAddButton}
        <div class="add-item-row">
          <Button size="medium" on:click={handleAddLineItem}>
            Add Item
          </Button>
        </div>
      {/if}
    </div>

    <div class="bank-totals-row">
      <div class="bank-details-container">
        <BankDetails />
      </div>

      {#if showTotals}
      <div class="invoice-totals">
        <div class="totals-row">
          <div class="totals-label">Subtotal:</div>
          <div class="totals-value">{formatCurrency(totals.subtotal)}</div>
        </div>

        <div class="totals-row">
          <div class="totals-label">Tax:</div>
          <div class="totals-value">{formatCurrency(totals.taxAmount)}</div>
        </div>

        {#if discountAmount > 0}
          <div class="totals-row">
            <div class="totals-label">Discount:</div>
            <div class="totals-value">-{formatCurrency(discountAmount)}</div>
          </div>
        {/if}

        <div class="totals-row total">
          <div class="totals-label">Total:</div>
          <div class="totals-value">{formatCurrency(totals.totalAmount - discountAmount)}</div>
        </div>
      </div>
    {/if}
    </div>

    
  </div>

  
  
</div>



<style lang="less">
  .line-items-container {
    width: 100%;
  }

  .bank-totals-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-top: 1rem;
    gap: 2rem;
  }

  .line-items-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    h3 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
    }
  }

  .tax-mode-selector {
    display: flex;
    align-items: center;
    gap: 8px;

    label {
      font-weight: 500;
      color: var(--black);
      font-size: 14px;
      white-space: nowrap;
    }

    select {
      padding: 4px 8px;
      border: 1px solid var(--border);
      border-radius: 4px;
      background: white;
      font-size: 14px;
      color: var(--black);

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }
  }

  .items-table {
    border: 1px solid var(--border);
    border-radius: 4px;
    width: 100%;

    &.readonly {
      .items-header {
        grid-template-columns: 3fr 1fr 1fr 1fr 1fr;
      }

      .items-row {
        grid-template-columns: 3fr 1fr 1fr 1fr 1fr;
      }
    }

    &:not(.readonly) {
      .items-header {
        grid-template-columns: 3fr 1fr 1fr 1fr 1fr;
      }

      .items-row {
        grid-template-columns: 3fr 1fr 1fr 1fr 1fr;
      }
    }
  }

  .items-header {
    display: grid;
    background-color: var(--bg);
    border-bottom: 1px solid var(--border);
    font-weight: 500;
    color: var(--grey);
    padding: 5px;

    .item-cell {
      padding: 10px;
      font-size: 14px;
      
      &.line-total {
        text-align: right;
      }
    }
  }

  .items-row {
    display: grid;
    border-bottom: 1px solid var(--border);
    align-items: flex-start;
    font-size: 14px;
    position: relative;
    padding: 5px;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      .actions, .item-cell .description-controls {
        display: flex;
      }
    }

    &.dragged {
      opacity: 0.5;
      transform: scale(0.98);
      z-index: 1000;
      pointer-events: none;
    }

    &.drag-over {
      border-top: 3px solid var(--primary);
    }

    &.drag-active:not(.dragged) {
      transition: transform 0.2s ease;
    }

    .actions {
        position: absolute;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        display: none;
        &.right {
          right: -30px;          
        }
        &.left {
          left: -30px;          
        }

        .drag-handle {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 22px;
          height: 22px;
          border-radius: calc(var(--br) / 1.5);
          cursor: grab;
          color: var(--grey);
          transition: all 0.2s ease;

          
          background: white;
          border: 1px solid var(--border);
          color: var(--black);

          &:hover:not(:disabled) {
            background: var(--border);
          }

          :global(svg) {
            :global(path) {
              fill: var(--primary);
            }
          }
        
          
          &:hover {
            background-color: var(--bg);
            color: var(--black);
          }
          
          &:active {
            cursor: grabbing;
            background-color: var(--primary-light);
            color: var(--primary);
          }

          &:focus {
            outline: 2px solid var(--primary);
            outline-offset: 2px;
          }
        }
      }

    .item-cell {
      padding: 10px;
      min-height: 40px;
      display: flex;
      align-items: center;

      &.amount {
        font-weight: 500;
        justify-content: flex-end;
      }
      
      input {
        width: 100%;
        padding: 8px;
        border: 1px solid var(--border);
        border-radius: 4px;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: var(--primary);
        }

        &.error {
          border-color: var(--red);
        }
      }

      .line-total-input {
        width: 100%;
        padding: 8px;
        border: 1px solid var(--border);
        border-radius: 4px;
        font-size: 14px;
        text-align: right;
        font-weight: 500;

        &:focus {
          outline: none;
          border-color: var(--primary);
        }
      }

      .readonly-value {
        text-align: center;
        width: 100%;
      }

      .description-content {
        width: 100%;

        .description-main {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .description-additional {
          font-size: 12px;
          color: var(--grey);
          font-style: italic;
        }
      }

      .description-controls {
        position: absolute;
        top: 5px;
        display: none;
        right: 5px;
      }

      .item-description {
        width: 100%;
        position: relative;
        
        .description-autocomplete {
          position: relative;
          width: 100%;
          
          .autocomplete-dropdown {
            position: absolute;
            top: calc(100% + 4px);
            left: 0;
            right: 0;
            max-height: 300px;
            overflow-y: auto;
            margin: 0;
            padding: 6px 0;
            background: white;
            border: 1px solid var(--border);
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            animation: fadeIn 0.15s ease-out;
            
            .dropdown-item {
              padding: 8px 12px;
              cursor: pointer;
              border-bottom: 1px solid var(--border);
              transition: background-color 0.2s;
              width: 100%;
              text-align: left;
              background: none;
              border-left: none;
              border-right: none;
              border-top: none;
              
              &:last-child {
                border-bottom: none;
              }
              
              &:hover:not(.no-results) {
                background-color: var(--bg);
              }
              
              &:focus {
                outline: 2px solid var(--primary);
                outline-offset: -2px;
                background-color: var(--bg);
              }
              
              &.custom-item {
                background-color: var(--primary-light);
                border-bottom: 2px solid var(--border);
                
                &:hover {
                  background-color: var(--primary-fade);
                }
                
                &:focus {
                  background-color: var(--primary-fade);
                }
                
                .item-name {
                  color: var(--primary);
                  font-weight: 600;
                }
              }
              
              &.no-results {
                cursor: default;
                background-color: var(--bg-light);
                
                .item-name {
                  color: var(--grey);
                  font-style: italic;
                }
              }
              
              .item-info {
                .item-name {
                  font-weight: 500;
                  margin-bottom: 2px;
                  font-size: 14px;
                }
                
                .item-description {
                  font-size: 12px;
                  color: var(--grey);
                  margin-bottom: 2px;
                }
                
                .item-price {
                  font-size: 12px;
                  color: var(--green);
                  font-weight: 500;
                }
              }
            }
          }
        }
        
        .additional-info {
          width: 100%;
          margin-top: 4px;
          padding: 6px 8px;
          border: 1px solid var(--border);
          border-radius: 4px;
          font-size: 12px;
          resize: vertical;
          font-family: inherit;
          
          &:focus {
            outline: none;
            border-color: var(--primary);
          }
          
          &::placeholder {
            color: var(--grey-light);
          }
        }
      }
    }
  }

  .add-item-row {
    padding: 10px;
    text-align: left;
    background-color: var(--bg);
  }

  .invoice-totals {
    // margin-top: 1rem;
    // padding: 20px;
    // background-color: var(--bg);
    // border: 1px solid var(--border);
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    margin-left: auto;
    padding-right: 1rem;

    .totals-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--border);

      &:last-child {
        border-bottom: none;
      }

      &.total {
        border-top: 1px solid var(--border);
        padding-top: 12px;
        font-weight: 600;
        font-size: 1.1em;
        color: var(--black);
        .totals-value {
          font-size: 1.3em;
          font-weight: 600;
        }
        .totals-label {
          font-size: 1.3em;
        }
      }

      .totals-label {
        color: var(--grey);
        font-weight: 500;
      }

      .totals-value {
        font-weight: 500;
        color: var(--black);
        text-align: right;
      }
    }
  }

  .error-message {
    color: var(--red);
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }

  // For table-style display in readonly mode
  @media (min-width: 768px) {
    .items-table.readonly {
      border: none;
      border-radius: 0;

      .items-header,
      .items-row {
        border: 1px solid var(--border);
        border-bottom: none;
      }

      .items-row:last-child {
        border-bottom: 1px solid var(--border);
      }
    }
  }

  @media (max-width: 768px) {
    .items-header,
    .items-row {
      grid-template-columns: 1fr !important;
      gap: 0.5rem;
    }

    .item-cell {
      padding: 0.5rem;
    }

    .items-header {
      display: none;
    }

    .items-row {
      display: block;
      border: 1px solid var(--border);
      border-radius: 4px;
      margin-bottom: 1rem;
      padding: 1rem;

      .item-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--border);

        &:last-child {
          border-bottom: none;
        }

        &::before {
          content: attr(data-label);
          font-weight: 500;
          color: var(--grey);
          flex: 1;
        }

        &.amount::before {
          content: "Total:";
        }
        
        &.actions::before {
          content: "";
        }
      }
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>