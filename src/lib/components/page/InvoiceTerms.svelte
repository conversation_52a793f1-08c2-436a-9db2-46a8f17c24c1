<script lang="ts">
    export let formData: any;
    export let mode: 'edit' | 'readonly' = 'edit';
</script>

<div class="form-group">
    <h3>Terms and Conditions</h3>
    {#if mode === 'readonly'}
        {#if formData.paymentTerms}
            <p class="readonly-text">{formData.paymentTerms}</p>
        {:else}
            <p class="readonly-text no-content">No payment terms specified</p>
        {/if}
    {:else}
        <textarea id="terms" bind:value={formData.paymentTerms} rows="4" placeholder="Payment terms and conditions"></textarea>
    {/if}
</div>

<style lang="less">
    h3 {
        margin-bottom: 1rem;
        font-weight: 600;
    }
    .readonly-text {
        margin: 0;
        // padding: 12px;
        // background-color: var(--bg);
        // border: 1px solid var(--border);
        border-radius: 4px;
        color: var(--black);
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;

        &.no-content {
            color: var(--grey);
            font-style: italic;
        }
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        font-size: 0.9rem;
        color: var(--grey);
    }

    .form-group {
        margin-bottom: 1rem;
    }
</style>