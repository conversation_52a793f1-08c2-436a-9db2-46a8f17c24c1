<script lang="ts">
    import Button from '$lib/components/Button.svelte';
    
    export let formData: any;
    export let formSubmitted: boolean = false;
    export let errors: Record<string, string> = {};
    export let mode: 'edit' | 'readonly' = 'edit';

    // Helper function to format date for display
    function formatDate(dateString: string): string {
        if (!dateString) return 'Not set';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit', 
            year: 'numeric'
        });
    }

    function handleAddField() {
        // Placeholder for add field functionality
        console.log('Add field clicked');
    }
</script>

<div class="invoice-details" class:edit-mode={mode === 'edit'}>

<h3>Invoice Details</h3>

<div class="form-group">
    <label for="invoiceNumber">Invoice No.</label>
    {#if mode === 'readonly'}
        <p class="readonly-text">{formData.invoiceNumber || 'Draft'}</p>
    {:else}
        <input
          type="number"
          id="invoiceNumber"
          bind:value={formData.invoiceNumber}
          placeholder="Auto-generated"
          class:error={formSubmitted && errors.invoiceNumber}
        />
        {#if formSubmitted && errors.invoiceNumber}
          <div class="error-message">{errors.invoiceNumber}</div>
        {/if}
    {/if}
</div>

<div class="form-group">
    <label for="issueDate">Issue Date</label>
    {#if mode === 'readonly'}
        <p class="readonly-text">{formatDate(formData.issueDate)}</p>
    {:else}
        <input
          type="date"
          id="issueDate"
          bind:value={formData.issueDate}
          class:error={formSubmitted && errors.issueDate}
        />
        {#if formSubmitted && errors.issueDate}
          <div class="error-message">{errors.issueDate}</div>
        {/if}
    {/if}
</div>

<div class="form-group">
    <label for="dueDate">Due Date</label>
    {#if mode === 'readonly'}
        <p class="readonly-text">{formatDate(formData.dueDate)}</p>
    {:else}
        <input
          type="date"
          id="dueDate"
          bind:value={formData.dueDate}
          class:error={formSubmitted && errors.dueDate}
        />
        {#if formSubmitted && errors.dueDate}
          <div class="error-message">{errors.dueDate}</div>
        {/if}
    {/if}
</div>

<div class="form-group">
    <label for="reference">Reference</label>
    {#if mode === 'readonly'}
        <p class="readonly-text">{formData.reference || 'Not set'}</p>
    {:else}
        <input
          type="text"
          id="reference"
          bind:value={formData.reference}
          placeholder="Optional reference"
          class:error={formSubmitted && errors.reference}
        />
        {#if formSubmitted && errors.reference}
          <div class="error-message">{errors.reference}</div>
        {/if}
    {/if}
</div>

{#if mode === 'edit'}
    <div class="add-field-container">
        <Button variant="secondary" size="small" on:click={handleAddField}>
            Add Field
        </Button>
    </div>
{/if}

</div>

<style lang="less">
    h3 {
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .invoice-details {
        display: flex;
        flex-direction: column;
        // gap: 1rem;
        &.edit-mode {
            gap: .5rem;
        }
    }

    .form-group {
        margin-bottom: 0px;
        flex-direction: row;
        align-items: center;
        label {
            width: 140px;
            margin-bottom: 0px;
        }
    }

    .readonly-text {
        margin: 0;
        // padding: 8px 0;
        color: var(--black);
        font-size: 14px;
        line-height: 1.5;
    }

    .add-field-container {
        margin-top: 1rem;
        display: flex;
        justify-content: flex-start;
    }

    :global(.form-group) {
        margin-bottom: 0px;
    }
</style>