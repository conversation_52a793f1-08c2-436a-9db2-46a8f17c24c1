<script lang="ts">
  // Props
  export let message: string = "Loading...";
  export let size: 'small' | 'medium' | 'large' = 'medium';
  export let variant: 'default' | 'inline' | 'white' = 'default';
</script>

<div class="loading-container {variant}">
  <div class="loading-spinner {size} {variant}"></div>
  {#if message && variant !== 'inline'}
    <p class="loading-message">{message}</p>
  {/if}
</div>

<style lang="less">
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: var(--grey);

    &.inline {
      padding: 0;
      margin: 0;
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
    }

    &.white {
      padding: 0;
      margin: 0;
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;
      color: white;
    }
  }

  .loading-spinner {
    border: 4px solid var(--secondary-fade);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;

    &.small {
      width: 24px;
      height: 24px;
      border-width: 3px;
    }

    &.medium {
      width: 40px;
      height: 40px;
      border-width: 4px;
    }

    &.large {
      width: 60px;
      height: 60px;
      border-width: 5px;
    }

    &.inline {
      margin-bottom: 0;
      width: 16px;
      height: 16px;
      border-width: 2px;
    }

    &.white {
      margin-bottom: 0;
      width: 16px;
      height: 16px;
      border-width: 2px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
    }
  }

  .loading-message {
    margin: 0;
    font-size: 14px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
